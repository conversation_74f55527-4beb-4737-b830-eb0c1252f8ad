package com.guanghonggu.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.UUID;

/**
 * 阿里云 OSS 签名工具类（只用于生成签名链接）
 */
@Component
public class AliyunOssUtil {

    @Value("${aliyun.oss.endpoint}")
    private String endpoint;

    @Value("${aliyun.oss.accessKeyId}")
    private String accessKeyId;

    @Value("${aliyun.oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${aliyun.oss.bucketName}")
    private String bucketName;

    /**
     * 生成签名访问链接（用于私有 OSS 图片临时访问）
     * @param objectName OSS 文件路径（不带域名）
     * @return 有效期1小时的签名链接
     */
    public String generatePresignedUrl(String objectName) {
        try {
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            Date expiration = new Date(new Date().getTime() + 3600L * 1000 * 24 * 365 * 10);
            URL url = ossClient.generatePresignedUrl(bucketName, objectName, expiration);
            // 强制将 http 转为 https（防止浏览器拒绝加载）
            String signedUrl = url.toString().replace("http://", "https://");
            return signedUrl;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 上传文件到阿里云 OSS，返回临时链接
     * @param file
     * @return
     */
    public String upload(MultipartFile file) {
        try {
            // 第1步：生成按日期分类的文件夹路径，比如 "2025/05/20"
            // 好处：文件不会全部堆在一个目录下，便于管理
            String datePath = new SimpleDateFormat("yyyy/MM/dd").format(new Date());
            // 第2步：获取用户上传的原始文件名（如 abc.jpg）
            String originalFilename = file.getOriginalFilename();
            // 第3步：生成不重复的新文件名（UUID + 原始后缀），如：af2d234f34f23f.jpg
            String fileName = UUID.randomUUID().toString().replace("-", "") +
                    originalFilename.substring(originalFilename.lastIndexOf("."));
            // 说明：
            //  - UUID 是保证唯一（防止覆盖）
            //  - substring(...) 获取的是原来的后缀（如 .jpg）
            // 第4步：拼接 OSS 中的完整路径（objectName，不含域名）
            // 例：2025/05/20/af2d234f34f23f.jpg
            String objectName = datePath + "/" + fileName;
            // 第5步：连接 OSS 客户端，准备上传
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            // 上传文件：把文件内容上传到你指定的 bucket + 路径（objectName）
            ossClient.putObject(bucketName, objectName, file.getInputStream());
            // 上传后要关闭客户端连接，释放资源
            ossClient.shutdown();
            // 第6步：生成带签名的完整访问链接（含域名 + objectName + ?签名参数）
            return generatePresignedUrl(objectName);
        } catch (Exception e) {
            // 发生异常时打印错误信息，并返回 null
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 删除阿里云，数据库图片
     */
    public void delete(String fullUrl) {
        // 打印完整的 OSS 文件链接（带签名参数）
        System.out.println("准备删除的完整 OSS URL: " + fullUrl);
        try {
            // 第一步：去掉链接末尾的参数（? 后面的签名、时间戳等）
            // fullUrl 形如：https://xxx.aliyuncs.com/2025/05/21/abc.png?Expires=xxxx
            // split("\\?") 表示用问号分隔，只保留前面部分，即去掉 ? 后面参数
            String urlWithoutParams = fullUrl.split("\\?")[0];
            System.out.println("去除参数后的 URL: " + urlWithoutParams);
            // 第二步：从链接中提取出真正的文件路径（objectName）
            // 假设现在是：https://xxx.aliyuncs.com/2025/05/21/abc.png
            // .split(".com/", 2) 表示用 ".com/" 分隔成两段：前面是域名，后面就是 objectName
            // 注意：不能写成 ".aliyuncs.com/"，因为 endpoint 可能变化
            String[] parts = urlWithoutParams.split(".com/", 2);
            // 判断是否成功提取
            if (parts.length < 2) {
                System.err.println("OSS路径格式错误，提取 objectName 失败");
                return;
            }
            // objectName 就是 OSS 中真实的路径，例如：2025/05/21/abc.png
            String objectName = parts[1];
            System.out.println("提取出的 objectName: " + objectName);
            // 第三步：连接 OSS 并执行删除操作
            // 使用你的配置创建 OSS 客户端
            OSS ossClient = new OSSClientBuilder().build(endpoint, accessKeyId, accessKeySecret);
            // 删除指定 bucket 中的文件
            ossClient.deleteObject(bucketName, objectName);
            // 删除成功提示
            System.out.println("OSS 删除成功！");
            // 关闭连接
            ossClient.shutdown();
        } catch (Exception e) {
            // 如果过程中出现异常，打印错误信息和详细堆栈
            System.err.println("OSS 删除失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 提取 objectName：从完整 OSS 链接中提取出路径
     */
    public String extractObjectName(String fullPath) {
        if (fullPath != null && fullPath.startsWith("http")) {
            String[] parts = fullPath.split(".com/", 2);
            if (parts.length == 2) {
                return parts[1];
            }
        }
        return fullPath;
    }

}
