package com.guanghonggu.util;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;

import java.util.Date;

public class JwtUtil {
    // 密钥（正式项目建议存在配置文件）
    private static final String SECRET_KEY = "mySecretKey123456";

    // 过期时间（例如：7 天）
    private static final long EXPIRATION = 1000 * 60 * 60 * 24 * 7;

    /**
     * 生成 token
     * @param adminId 管理员 ID
     * @param username 用户名
     * @param accountPermission 账号权限
     * @return 生成的 JWT token
     */
    public static String generateToken(Long adminId, String username, Integer accountPermission) {
        return Jwts.builder()
                .setSubject(username)  // 设置用户名为 subject
                .claim("adminId", adminId)  // 将管理员 ID 放入 token
                .claim("accountPermission", accountPermission)  // 将权限信息放入 token
                .setIssuedAt(new Date())  // 设置签发时间
                .setExpiration(new Date(System.currentTimeMillis() + EXPIRATION))  // 设置过期时间
                .signWith(SignatureAlgorithm.HS256, SECRET_KEY)  // 使用密钥进行签名
                .compact();
    }

    /**
     * 解析 token
     * @param token JWT token
     * @return Claims 信息
     */
    public static Claims parseToken(String token) {
        return Jwts.parser()
                .setSigningKey(SECRET_KEY)
                .parseClaimsJws(token)
                .getBody();
    }

    /**
     * 判断 token 是否过期
     * @param token JWT token
     * @return 是否过期
     */
    public static boolean isTokenExpired(String token) {
        return parseToken(token).getExpiration().before(new Date());
    }

    /**
     * 从 token 中提取权限信息
     * @param token JWT token
     * @return 权限信息
     */
    public static Integer extractAccountPermission(String token) {
        return parseToken(token).get("accountPermission", Integer.class);  // 提取权限信息
    }
}
