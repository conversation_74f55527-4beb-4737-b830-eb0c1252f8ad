package com.guanghonggu.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderWithRejectReasonVO {

    // order 表字段（必须有）
    private Long orderId;
    private Long userId;
    private BigDecimal actualPaymentPrice;

    // refund_reject_reason 表字段（新增）
    private String rejectReason; // 拒绝理由
    private Date createTime;

    //user表字段
    private String nickname;
}