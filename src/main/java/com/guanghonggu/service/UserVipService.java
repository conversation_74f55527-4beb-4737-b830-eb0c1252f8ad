package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guanghonggu.dto.UserVipDTO;
import com.guanghonggu.entity.UserVip;

import java.util.List;

public interface UserVipService extends IService<UserVip> {

    List<UserVip> getUserVip();

    UserVip getUserVipByVipId(UserVipDTO userVipDTO);

    int addUserVip(UserVipDTO userVipDTO);

    int updateUserVip(UserVipDTO userVipDTO);

    int deleteUserVip(UserVipDTO userVipDTO);
}
