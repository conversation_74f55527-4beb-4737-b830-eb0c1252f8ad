package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guanghonggu.dto.GiftCouponProbabilityDTO;
import com.guanghonggu.entity.GiftCouponProbability;

import java.util.List;

public interface GiftCouponProbabilityService extends IService<GiftCouponProbability> {
    List<GiftCouponProbability> getGiftCouponProbability();

    GiftCouponProbability getGiftCouponProbabilityById(GiftCouponProbabilityDTO giftCouponProbabilityDTO);

    int addGiftCouponProbability(GiftCouponProbabilityDTO giftCouponProbabilityDTO);

    int updateGiftCouponProbability(GiftCouponProbabilityDTO giftCouponProbabilityDTO);

    int deleteGiftCouponProbability(GiftCouponProbabilityDTO giftCouponProbabilityDTO);
}
