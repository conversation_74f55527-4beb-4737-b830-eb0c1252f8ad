package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guanghonggu.dto.CaregiverDTO;
import com.guanghonggu.entity.Caregiver;

import java.util.List;

public interface CaregiverService extends IService<Caregiver> {
    /**
     * 查询全部阿姨信息
     */
    Page<Caregiver> getAllCaregiver(Integer page,Integer size);

    /**
     * 根据阿姨id查询
     */
    List<Caregiver> getCaregiverId(CaregiverDTO caregiverDTO);

    /**
     * 获取审核图片
     */
    CaregiverDTO getAuditImages(Long caregiverId);

    /**
     * 审核图片是否通过
     */
    int updateCaregiver(CaregiverDTO caregiverDTO);
}
