package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guanghonggu.dto.CaregiverServiceTypeDTO;
import com.guanghonggu.entity.CaregiverServiceType;

import java.util.List;

public interface CaregiverServiceTypeService extends IService<CaregiverServiceType> {

    /**
     * 查询阿姨权限
     */
    List<CaregiverServiceType> queryPermissionCaregiverServiceType(CaregiverServiceTypeDTO caregiverServiceTypeDTO);

    /**
     * 添加阿姨权限
     */
    void addCaregiverServiceTypes(List<CaregiverServiceType> caregiverServiceTypes);

    /**
     * 删除阿姨权限
     */
    long deletePermissionCaregiverServiceType(CaregiverServiceTypeDTO caregiverServiceTypeDTO);

}
