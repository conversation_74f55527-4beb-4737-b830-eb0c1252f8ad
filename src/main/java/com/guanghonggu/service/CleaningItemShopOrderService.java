package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.CleaningItemShopOrderDTO;
import com.guanghonggu.entity.CleaningItemShopOrder;

public interface CleaningItemShopOrderService {

    Page<CleaningItemShopOrder> getAllCleaningItemShopOrder(Integer page, Integer size, CleaningItemShopOrderDTO cleaningItemShopOrderDTO);

    CleaningItemShopOrder getCleaningItemShopOrder(CleaningItemShopOrderDTO cleaningItemShopOrderDTO);

    Integer modifyOrderStatus(CleaningItemShopOrderDTO cleaningItemShopOrderDTO);
}
