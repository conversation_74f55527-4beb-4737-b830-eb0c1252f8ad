package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.StatisticsDTO;
import com.guanghonggu.dto.UserDTO;
import com.guanghonggu.entity.User;

import java.util.List;

public interface UserService extends IService<User> {

    /**
     * 查询用户信息
     */
    Page<User> getAllUser(Integer page, Integer size);

    /**
     * 根据用户id查询
     */
    List<User> getUserId(UserDTO userDTO);
}
