package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserCouponDTO;
import com.guanghonggu.entity.Coupon;
import com.guanghonggu.entity.UserCoupon;

import java.util.Date;

public interface UserCouponService extends IService<UserCoupon> {
    ResultDTO<String> distributeCoupon(UserCouponDTO userCouponDTO);

    // 校验用户是否符合领取优惠券的条件
    ResultDTO<String> checkUserEligibility(Long userId, Coupon coupon);

    // 计算优惠券的过期时间
    Date calculateCouponExpirationTime(Coupon coupon);
}
