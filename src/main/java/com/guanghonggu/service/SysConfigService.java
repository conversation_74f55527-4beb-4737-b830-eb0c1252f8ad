package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.SysConfigDTO;
import com.guanghonggu.entity.SysConfig;

import java.math.BigDecimal;
import java.util.List;

public interface SysConfigService {
    int addSysConfig(SysConfigDTO sysConfigDTO);
    int updateSysConfig(SysConfigDTO sysConfigDTO);
    List<SysConfig> getIdSysConfig(SysConfigDTO sysConfigDTO);

    Page<SysConfig> getAllSysConfig(Integer page, Integer size);

    int deleteSysConfig(SysConfigDTO sysConfigDTO);

    BigDecimal getPlatformRatio(); // 获取平台抽成比例

    BigDecimal getRatioByKey(String key);

}
