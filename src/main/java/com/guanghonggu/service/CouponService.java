package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.guanghonggu.dto.CouponCreateDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Coupon;

public interface CouponService extends IService<Coupon> {
    /**
     * 创建优惠券
     */
    ResultDTO<String> postCoupon(CouponCreateDTO couponCreateDTO);
    /**
     * 查询优惠券
     */
    Page<Coupon> getAllCoupon(Integer page, Integer size,Integer isDelete);
    /**
     * 编辑优惠券
     */
    int updateCoupon(CouponCreateDTO couponCreateDTO);
    /**
     * 删除优惠券
     */
    int deleteCoupon(CouponCreateDTO couponCreateDTO);
}
