package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.vo.OrderWithRejectReasonVO;
import com.guanghonggu.dto.OrderDTO;
import com.guanghonggu.dto.StatisticsDTO;
import com.guanghonggu.entity.Order;

import java.util.List;
import java.util.Map;


public interface OrderService {

    Page<Order> getAllOrder(Integer page, Integer size);

    /**
     * 根据订单编号查询
     */
    List<Order> getOrderId(OrderDTO orderDTO);

    /**
     * 根据订单id查询订单信信息
     */
    List<OrderDTO> getOrderById(Long id);

    //收入
    /**
     * 获取订单收入、充值收入及总收入
     */
    StatisticsDTO getIncomeStatistics();

    /**
     * 按年查询收入
     */
    StatisticsDTO getIncomeByYear(int year);

    /**
     * 按月查询收入
     */
    StatisticsDTO getIncomeByMonth(int year, int month);

    /**
     * 按周查询收入（week 以 ISO 8601 标准，1 为周一）
     */
    StatisticsDTO getIncomeByWeek(int year, int week);

    /**
     * 按日查询收入（date 格式：YYYY-MM-DD）
     */
    StatisticsDTO getIncomeByDay(String date);

    //支出
    /**
     * 获取订单支出、充值支出及总支出
     */
    StatisticsDTO queryAll();

    /**
     *按年查询支出
     */
    StatisticsDTO queryByYear(int year);

    /**
     *按月查询支出
     */
    StatisticsDTO queryByMonth(int year, int month);

    /**
     *按周查询支出
     */
    StatisticsDTO queryByWeek(int year, int week);

    /**
     *按日查询支出（date 格式：YYYY-MM-DD）
     */
    StatisticsDTO queryByDay(String date);

    //盈利
    /**
     * 获取订单盈利、充值盈利及总盈利
     */
    StatisticsDTO queryProfitAll();
    /**
     * 按年查询盈利
     */
    StatisticsDTO queryProfitByYear(int year);
    /**
     * 按月查询盈利
     */
    StatisticsDTO queryProfitByMonth(int year, int month);
    /**
     * 按周查询盈利
     */
    StatisticsDTO queryProfitByWeek(int year, int week);
    /**
     * 按日查询盈利（格式 YYYY-MM-DD）
     */
    StatisticsDTO queryProfitByDay(String date);

    /**
     * 获取所有服务类型及其对应的订单数量
     */
    List<StatisticsDTO> getOrderTypeCounts();

    /**
     * 根据地址查询订单数量
     */
    List<StatisticsDTO> countOrderTypeByRegionWithCondition(Map<String, Object> params);

    /**
     *查询退款审批
     */
    Page<OrderWithRejectReasonVO> getOrderStatus(Integer page, Integer size, OrderDTO orderDTO, Integer status);

    /**
     * 退款审批
     */
    int updateStatus(OrderDTO orderDTO);



}