package com.guanghonggu.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.MessageDTO;
import com.guanghonggu.entity.Message;

public interface MessageService {
    /**
     * 查询系统通知
     */
    Page<Message> getAllMessage(Integer page,Integer size);

    /**
     * 新增系统通知
     */
    int addMessage(MessageDTO messageDTO);

    /**
     * 编辑系统通知
     */
    int updateMessage(MessageDTO messageDTO);

    /**
     * 删除系统通知
     */
    int deleteMessage(MessageDTO messageDTO);
}
