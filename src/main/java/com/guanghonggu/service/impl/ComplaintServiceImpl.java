package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.service.ComplaintService;
import com.guanghonggu.entity.Complaint;
import com.guanghonggu.mapper.ComplaintMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ComplaintServiceImpl extends ServiceImpl<ComplaintMapper, Complaint>
        implements ComplaintService {
    @Autowired
    private ComplaintMapper complaintMapper;

    @Override
    public Page<Complaint> getComplaint(Integer page, Integer size){
        return complaintMapper.getAllComplaintWithCaregiverName(new Page<>(page, size), page, size);
    }

}
