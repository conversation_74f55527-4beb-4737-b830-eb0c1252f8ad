package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.constant.HttpStatusConstants;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserCouponDTO;
import com.guanghonggu.entity.Coupon;
import com.guanghonggu.entity.UserCoupon;
import com.guanghonggu.mapper.CouponMapper;
import com.guanghonggu.mapper.UserCouponMapper;
import com.guanghonggu.mapper.UserMapper;
import com.guanghonggu.service.UserCouponService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import java.util.List;


@Service
public class UserCouponServiceImpl extends ServiceImpl<UserCouponMapper, UserCoupon>
        implements UserCouponService {
    @Autowired
    private UserCouponMapper userCouponMapper;
    @Autowired
    private CouponMapper couponMapper;
    @Autowired
    private UserMapper userMapper;
    @Autowired
    private PlatformTransactionManager txManager;

    @Override
    public ResultDTO<String> distributeCoupon(UserCouponDTO userCouponDTO) {
        Long couponId = userCouponDTO.getCouponId();

        TransactionDefinition definition = TransactionDefinition.withDefaults();
        TransactionStatus transaction = txManager.getTransaction(definition);
        try {
            Coupon coupon = couponMapper.getCouponById(couponId);
            if (coupon == null) {
                return ResultDTO.error("优惠券不存在");
            }

            List<Long> allUserIds = userMapper.getAllUserIds();
            if (allUserIds.isEmpty()) {
                return ResultDTO.error("没有用户下发优惠券");
            }

            for (Long userId : allUserIds) {
                // 校验用户是否符合领取条件
                ResultDTO<String> checkResult = checkUserEligibility(userId, coupon);
                if (checkResult.getCode() == HttpStatusConstants.ERROR) {
                    continue;  // 如果用户不符合条件，跳过
                }

                // 检查用户是否已经领取过该优惠券
                UserCoupon existingUserCoupon = userCouponMapper.getUserCoupon(userId, couponId);
                if (existingUserCoupon != null) {
                    // 如果该用户已领取过优惠券，跳过
                    continue;
                }

                //更新优惠券剩余数量
                int res = couponMapper.updateCouponRemainingQuantity(couponId);
                if (res <= 0) {
                    return ResultDTO.error("优惠券已领完");
                }

                Date expirationTime = calculateCouponExpirationTime(coupon);

                // 5. 插入用户优惠券记录
                UserCoupon userCoupon = new UserCoupon();
                userCoupon.setUserId(userId);
                userCoupon.setCouponId(couponId);
                userCoupon.setAcquireTime(new Date());
                userCoupon.setExpirationTime(expirationTime);
                userCoupon.setStatus(1);
                userCouponMapper.insert(userCoupon);
            }
                // 提交事务
                txManager.commit(transaction);

                return ResultDTO.success("下发优惠券成功", null);
        } catch (Exception e) {
            txManager.rollback(transaction);
            return ResultDTO.error("优惠券下发失败: " + e.getMessage());
        }

    }

    @Override
    public ResultDTO<String> checkUserEligibility(Long userId, Coupon coupon) {
        // 检查用户是否符合领取优惠券的条件
        // 目前假设不需要额外的判断，后续可以根据业务需求补充逻辑
        return ResultDTO.success("",null);
    }

    @Override
    public Date calculateCouponExpirationTime(Coupon coupon) {
        // 计算优惠券的过期时间
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime expirationDate = now.plusDays(coupon.getValidDay()); // 使用优惠券的有效天数计算到期时间
        return Date.from(expirationDate.atZone(ZoneId.systemDefault()).toInstant());
    }

}
