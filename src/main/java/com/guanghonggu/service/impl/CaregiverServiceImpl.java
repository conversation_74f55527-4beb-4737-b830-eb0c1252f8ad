package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.mapper.CaregiverMapper;
import com.guanghonggu.service.CaregiverService;
import com.guanghonggu.dto.CaregiverDTO;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.util.AliyunOssUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.net.URL;
import java.util.List;


@Service
public class CaregiverServiceImpl extends ServiceImpl<CaregiverMapper, Caregiver>
        implements CaregiverService {

    @Autowired
    private CaregiverMapper caregiverMapper;

    @Autowired
    private AliyunOssUtil aliyunOssUtil;

    /**
     * 查询全部阿姨信息
     */
    @Override
    public Page<Caregiver> getAllCaregiver(Integer page, Integer size) {
        Page<Caregiver> caregiverPage = new Page<>(page, size);
        QueryWrapper<Caregiver> queryWrapper = new QueryWrapper<>();
        return caregiverMapper.selectPage(caregiverPage, queryWrapper);
    }

    /**
     * 根据阿姨id查询
     */
    @Override
    public List<Caregiver> getCaregiverId(CaregiverDTO caregiverDTO){
        LambdaQueryWrapper<Caregiver> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Caregiver :: getCaregiverId, caregiverDTO.getCaregiverId());
        return caregiverMapper.selectList(queryWrapper);
    }


    @Override
    public CaregiverDTO getAuditImages(Long caregiverId) {
        Caregiver caregiver = caregiverMapper.selectById(caregiverId);
        if (caregiver == null) {
            throw new RuntimeException("阿姨不存在");
        }

        CaregiverDTO dto = new CaregiverDTO();

        // 安全调用每一个字段
        dto.setIdCardFront(caregiver.getIdCardFront() != null
                ? aliyunOssUtil.generatePresignedUrl(extractObjectName(caregiver.getIdCardFront()))
                : null);

        dto.setIdCardBack(caregiver.getIdCardBack() != null
                ? aliyunOssUtil.generatePresignedUrl(extractObjectName(caregiver.getIdCardBack()))
                : null);

        dto.setHealthCertificate(caregiver.getHealthCertificate() != null
                ? aliyunOssUtil.generatePresignedUrl(extractObjectName(caregiver.getHealthCertificate()))
                : null);

        return dto;
    }

    /**
     * 从完整 URL 中提取 OSS 对象名（去除域名和参数部分）
     * 例如输入：https://bucket.oss-cn-beijing.aliyuncs.com/caregiver/xxx.png
     * 返回：caregiver/xxx.png
     */
    private String extractObjectName(String fullUrl) {
        try {
            URL url = new URL(fullUrl);             // 解析 URL 对象
            return url.getPath().substring(1);      // 去掉开头的 "/"，得到对象名
        } catch (Exception e) {
            throw new RuntimeException("OSS 地址格式错误，无法提取对象名", e);
        }
    }

    @Override
    public int updateCaregiver(CaregiverDTO caregiverDTO) {
        QueryWrapper<Caregiver> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("caregiver_id", caregiverDTO.getCaregiverId());

        Caregiver caregiver = caregiverMapper.selectOne(queryWrapper);
        if (caregiver == null) {
            throw new RuntimeException("阿姨不存在");
        }

        Integer idCardStatus = caregiverDTO.getIdCardVerification();
        Integer healthCertStatus = caregiverDTO.getHealthCertificateVerification();
        String auditRemark = caregiverDTO.getAuditRemark();

        // 如果两项都通过，则不能填写拒绝理由
        if (idCardStatus == 1 && healthCertStatus == 1) {
            if (auditRemark != null && !auditRemark.trim().isEmpty()) {
                throw new RuntimeException("审核全部通过，不能填写拒绝理由");
            }
            caregiverDTO.setAuditRemark(null); // 保证清空
        } else {
            // 有任何一项失败，必须填写拒绝理由
            if (auditRemark == null || auditRemark.trim().isEmpty()) {
                throw new RuntimeException("请填写拒绝理由");
            }
        }

        BeanUtils.copyProperties(caregiverDTO, caregiver);
        return caregiverMapper.updateById(caregiver);
    }
}
