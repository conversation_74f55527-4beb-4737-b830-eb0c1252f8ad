package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.service.SysConfigService;
import com.guanghonggu.vo.OrderWithRejectReasonVO;
import com.guanghonggu.dto.OrderDTO;
import com.guanghonggu.dto.ServiceVerificationImageDTO;
import com.guanghonggu.entity.*;
import com.guanghonggu.mapper.*;
import com.guanghonggu.service.OrderService;
import com.guanghonggu.dto.StatisticsDTO;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class OrderServiceImpl implements OrderService {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private WalletTransactionMapper walletTransactionMapper;
    @Autowired
    private RefundRejectReasonMapper refundRejectReasonMapper;
    @Autowired
    private WalletMapper walletMapper;
    @Autowired
    private SysConfigService sysConfigService;

    @Override
    public Page<Order> getAllOrder(Integer page, Integer size){
        return orderMapper.getAllOrderWithCaregiverName(new Page<>(page, size), page, size);
    }

    /**
     * 根据订单编号查询
     */
    @Override
    public List<Order> getOrderId(OrderDTO orderDTO){
        return orderMapper.getOrderWithCaregiverAndUser(orderDTO);
    }

    /**
     * 根据订单id查询订单信息
     */
    @Override
    public List<OrderDTO> getOrderById(Long id) {
        // 查询订单信息，返回OrderDTO
        List<OrderDTO> orderDTOList = orderMapper.getOrderById(id);

        // 如果订单列表不为空，取第一个订单信息
        if (orderDTOList != null && !orderDTOList.isEmpty()) {
            OrderDTO orderDTO = orderDTOList.get(0);  // 获取第一个订单

            // 获取service_verification_id
            Long serviceVerificationId = orderDTO.getServiceVerificationId();

            // 查询与service_verification_id关联的所有图片
            if (serviceVerificationId != null) {
                List<ServiceVerificationImageDTO> serviceVerificationImages = orderMapper.getServiceVerificationImagesByOrderId(serviceVerificationId);
                orderDTO.setServiceVerificationImages(serviceVerificationImages);  // 设置图片信息到OrderDTO中
            }

            return Collections.singletonList(orderDTO);  // 返回一个包含一个订单DTO的列表
        }

        return Collections.emptyList();  // 如果没有查询到订单，返回空列表
    }


    /**
     * 收入
     */
    @Override
    public StatisticsDTO getIncomeStatistics() {
        List<Integer> transactionTypes = Arrays.asList(1, 2); // transaction_type 为 1 或 2
        // 只计算 wallet_transaction 表中的收入
        return buildStats(
                BigDecimal.ZERO, // 不使用原有的订单收入逻辑
                walletTransactionMapper.sumExternalAmountByConditions(1, transactionTypes, 1) // 仅计算符合条件的收入
        );
    }
    @Override
    public StatisticsDTO getIncomeByYear(int year) {
        return buildStats(orderMapper.sumOrderIncomeByYear(year), walletTransactionMapper.sumRechargeIncomeByYear(year));
    }
    @Override
    public StatisticsDTO getIncomeByMonth(int year,int month) {
        return buildStats(orderMapper.sumOrderIncomeByMonth(year,month), walletTransactionMapper.sumRechargeIncomeByMonth(year,month));
    }
    @Override
    public StatisticsDTO getIncomeByWeek(int year,int week) {
        return buildStats(orderMapper.sumOrderIncomeByWeek(year,week), walletTransactionMapper.sumRechargeIncomeByWeek(year,week));
    }
    @Override
    public StatisticsDTO getIncomeByDay(String date) {
        return buildStats(orderMapper.sumOrderIncomeByDay(date), walletTransactionMapper.sumRechargeIncomeByDay(date));
    }
    private StatisticsDTO buildStats(BigDecimal o, BigDecimal r) {
        BigDecimal order = o==null?BigDecimal.ZERO:o;
        BigDecimal recharge = r==null?BigDecimal.ZERO:r;
        StatisticsDTO stats=new StatisticsDTO();
        stats.setOrderIncome(order);
        stats.setRechargeIncome(recharge);
        stats.setTotalIncome(order.add(recharge));
        return stats;
    }


    /**
     * 支出
     */
    @Override
    public StatisticsDTO queryAll(){
        return buildExpenseStats(orderMapper.sumOrderExpense(),walletTransactionMapper.sumWithdrawExpense());
    }
    @Override
    public StatisticsDTO queryByYear(int year) {
        return buildExpenseStats(orderMapper.sumOrderExpenseByYear(year), walletTransactionMapper.sumWithdrawExpenseByYear(year));
    }
    @Override
    public StatisticsDTO queryByMonth(int year,int month) {
        return buildExpenseStats(orderMapper.sumOrderExpenseByMonth(year,month), walletTransactionMapper.sumWithdrawExpenseByMonth(year,month));
    }
    @Override
    public StatisticsDTO queryByWeek(int year,int week) {
        return buildExpenseStats(orderMapper.sumOrderExpenseByWeek(year,week), walletTransactionMapper.sumWithdrawExpenseByWeek(year,week));
    }
    @Override
    public StatisticsDTO queryByDay(String date) {
        return buildExpenseStats(orderMapper.sumOrderExpenseByDay(date), walletTransactionMapper.sumWithdrawExpenseByDay(date));
    }

    private StatisticsDTO buildExpenseStats(BigDecimal orderExp, BigDecimal withdrawExp) {
        BigDecimal o = orderExp   == null ? BigDecimal.ZERO : orderExp;
        BigDecimal w = withdrawExp== null ? BigDecimal.ZERO : withdrawExp;
        StatisticsDTO stats = new StatisticsDTO();
        stats.setOrderExpense(o);
        stats.setWithdrawExpense(w);
        stats.setTotalExpense(o.add(w));
        return stats;
    }

    /**
     * 盈利
     */
    @Override
    public StatisticsDTO queryProfitAll() {
        // 订单收入直接使用 merchant_income 字段
        BigDecimal orderIncome = orderMapper.sumMerchantIncome();

        // 充值和提现保持不变
        BigDecimal rechargeIncome = walletTransactionMapper.sumRechargeIncome();
        BigDecimal withdrawExpense = walletTransactionMapper.sumWithdrawExpense();

        // 计算盈利：订单收入 + 充值收入 - 提现支出
        BigDecimal orderProfit = safe(orderIncome);
        BigDecimal rechargeProfit = safe(rechargeIncome).subtract(safe(withdrawExpense));

        return buildProfitStats(orderProfit, rechargeProfit);
    }

    @Override
    public StatisticsDTO queryProfitByYear(int year) {
        BigDecimal oi = orderMapper.sumMerchantIncomeByYear(year);
        BigDecimal ri = walletTransactionMapper.sumRechargeIncomeByYear(year);
        BigDecimal we = walletTransactionMapper.sumWithdrawExpenseByYear(year);

        return buildProfitStats(
                safe(oi),
                safe(ri).subtract(safe(we))
        );
    }

    @Override
    public StatisticsDTO queryProfitByMonth(int year, int month) {
        BigDecimal oi = orderMapper.sumMerchantIncomeByMonth(year, month);
        BigDecimal ri = walletTransactionMapper.sumRechargeIncomeByMonth(year, month);
        BigDecimal we = walletTransactionMapper.sumWithdrawExpenseByMonth(year, month);

        return buildProfitStats(
                safe(oi),
                safe(ri).subtract(safe(we))
        );
    }

    @Override
    public StatisticsDTO queryProfitByWeek(int year, int week) {
        BigDecimal oi = orderMapper.sumMerchantIncomeByWeek(year, week);
        BigDecimal ri = walletTransactionMapper.sumRechargeIncomeByWeek(year, week);
        BigDecimal we = walletTransactionMapper.sumWithdrawExpenseByWeek(year, week);

        return buildProfitStats(
                safe(oi),
                safe(ri).subtract(safe(we))
        );
    }

    @Override
    public StatisticsDTO queryProfitByDay(String date) {
        BigDecimal oi = orderMapper.sumMerchantIncomeByDay(date);
        BigDecimal ri = walletTransactionMapper.sumRechargeIncomeByDay(date);
        BigDecimal we = walletTransactionMapper.sumWithdrawExpenseByDay(date);

        return buildProfitStats(
                safe(oi),
                safe(ri).subtract(safe(we))
        );
    }

    /**
     * 辅助方法：如果是 null，就返回 0
     */
    private BigDecimal safe(BigDecimal v) {
        return v == null ? BigDecimal.ZERO : v;
    }

    /**
     * 构建盈利统计对象
     */
    private StatisticsDTO buildProfitStats(BigDecimal orderProfit, BigDecimal rechargeProfit) {
        StatisticsDTO stats = new StatisticsDTO();
        stats.setOrderProfit(orderProfit);
        stats.setRechargeProfit(rechargeProfit);
        stats.setTotalProfit(orderProfit.add(rechargeProfit));
        return stats;
    }
    /**
     * 获取所有服务类型及其对应的订单数量
     */
    @Override
    public List<StatisticsDTO> getOrderTypeCounts() {
        return orderMapper.countByServiceType();
    }

    /**
     * 地区下单类型数量统计接口
     */
    @Override
    public List<StatisticsDTO> countOrderTypeByRegionWithCondition(Map<String, Object> params) {
        // 检查参数是否为空
        if (params == null || !params.containsKey("area") || !params.containsKey("serviceTypeId")) {
            throw new IllegalArgumentException("参数缺失：area 或 service_type_id");
        }

        // 获取 area 和 serviceTypeId，增加空值和格式检查
        String area = "";
        try {
            area = String.valueOf(params.get("area").toString());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("area 参数格式错误，必须是字符串");
        }

        Long serviceTypeId = null;
        try {
            serviceTypeId = Long.valueOf(params.get("serviceTypeId").toString());
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("service_type_id 参数格式错误，必须是数字");
        }

        // 调用数据层查询方法
        return orderMapper.countOrderTypeByRegionWithCondition(area, serviceTypeId);
    }

    @Override
    public Page<OrderWithRejectReasonVO> getOrderStatus(Integer page, Integer size, OrderDTO orderDTO, Integer status) {
        // 1. 构建分页对象和查询条件
        Page<Order> pageOrder = new Page<>(page, size);
        QueryWrapper<Order> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", status);

        // 2. 执行分页查询
        Page<Order> rawPage = orderMapper.selectPage(pageOrder, queryWrapper);

        // 3. 封装成 VO 列表
        List<OrderWithRejectReasonVO> voList = rawPage.getRecords().stream().map(order -> {
            OrderWithRejectReasonVO vo = new OrderWithRejectReasonVO();
            BeanUtils.copyProperties(order, vo);

            // 4. 查询 refund_reject_reason 表，获取 reject_reason
            LambdaQueryWrapper<RefundRejectReason> reasonWrapper = new LambdaQueryWrapper<>();
            reasonWrapper.eq(RefundRejectReason::getRefundId, order.getOrderId());
            RefundRejectReason reason = refundRejectReasonMapper.selectOne(reasonWrapper);


            if (reason != null) {
                vo.setRejectReason(reason.getRejectReason());
            }

            return vo;
        }).collect(Collectors.toList());

        // 5. 返回 VO 分页对象
        Page<OrderWithRejectReasonVO> resultPage = new Page<>();
        resultPage.setCurrent(rawPage.getCurrent());
        resultPage.setSize(rawPage.getSize());
        resultPage.setTotal(rawPage.getTotal());
        resultPage.setRecords(voList);


        return resultPage;
    }


    @Override
    public int updateStatus(OrderDTO orderDTO) {
        Long orderId = orderDTO.getOrderId();
        Order oldOrder = orderMapper.selectById(orderId);
        if (oldOrder == null) {
            throw new RuntimeException("订单不存在");
        }

        // 只处理：状态从 5 → 6 的退款操作
        if (oldOrder.getStatus() == 5 && orderDTO.getStatus() == 6) {

            // ========== 1. 提取关键数据 ==========
            BigDecimal originalAmount = oldOrder.getActualPaymentPrice(); // 原付款金额
            BigDecimal refundAmount = orderDTO.getRefundAmount();         // 实际退款金额
            BigDecimal platformRatio = sysConfigService.getRatioByKey("commission_rate"); // 从 sys_config 读取平台抽成比例

            if (originalAmount == null || refundAmount == null) {
                throw new RuntimeException("退款金额或原金额不能为空");
            }

            if (refundAmount.compareTo(originalAmount) > 0) {
                throw new RuntimeException("退款金额不能大于原订单实付金额");
            }

            // ========== 2. 计算分成 ==========
            BigDecimal remaining = originalAmount.subtract(refundAmount);        // 用户没退的钱
            BigDecimal merchantIncome = remaining.multiply(platformRatio);      // 平台收入
            BigDecimal caregiverIncome = remaining.subtract(merchantIncome);    // 服务人员收入

            // ========== 3. 钱包退款 ==========
            LambdaQueryWrapper<Wallet> walletWrapper = new LambdaQueryWrapper<>();
            walletWrapper.eq(Wallet::getUserId, oldOrder.getUserId());
            Wallet wallet = walletMapper.selectOne(walletWrapper);
            if (wallet == null) {
                throw new RuntimeException("该用户没有钱包信息");
            }

            wallet.setBalance(wallet.getBalance().add(refundAmount));
            wallet.setUpdateTime(new Date());
            walletMapper.updateById(wallet);

            // ========== 4. 查询原交易记录 ==========
            LambdaQueryWrapper<WalletTransaction> transWrapper = new LambdaQueryWrapper<>();
            transWrapper.eq(WalletTransaction::getOutTradeNo, oldOrder.getOrderNumber())
                    .eq(WalletTransaction::getTransactionType, 2); // 2 = 消费
            List<WalletTransaction> originalTransList = walletTransactionMapper.selectList(transWrapper);
            if (originalTransList == null || originalTransList.isEmpty()) {
                throw new RuntimeException("找不到原始支付记录");
            }
            WalletTransaction originalTrans = originalTransList.get(0);

            // ========== 5. 插入退款记录 ==========
            WalletTransaction refundTrans = new WalletTransaction();
            refundTrans.setWalletId(wallet.getWalletId());
            refundTrans.setTransactionType(4); // 退款
            refundTrans.setPaymentChannel(originalTrans.getPaymentChannel());
            refundTrans.setAmount(refundAmount);
            refundTrans.setTransactionTime(new Date());
            refundTrans.setStatus(1);
            refundTrans.setCreateTime(new Date());
            refundTrans.setUpdateTime(new Date());
            refundTrans.setOutTradeNo(originalTrans.getOutTradeNo());
            refundTrans.setExternalTradeNo(originalTrans.getExternalTradeNo());
            refundTrans.setBalanceAmount(originalTrans.getBalanceAmount());
            refundTrans.setRechargeAmount(originalTrans.getRechargeAmount());
            refundTrans.setExternalAmount(originalTrans.getExternalAmount());
            walletTransactionMapper.insert(refundTrans);

            // ========== 6. 插入服务人员收入记录（类型 5） ==========
            WalletTransaction caregiverTrans = new WalletTransaction();
            caregiverTrans.setTransactionType(5); // 阿姨收入
            caregiverTrans.setAmount(caregiverIncome);
            caregiverTrans.setTransactionTime(new Date());
            caregiverTrans.setStatus(1);
            caregiverTrans.setCreateTime(new Date());
            caregiverTrans.setUpdateTime(new Date());
            caregiverTrans.setOutTradeNo(oldOrder.getOrderNumber() + "_caregiver");
            walletTransactionMapper.insert(caregiverTrans);
            oldOrder.setCaregiverIncome(caregiverIncome);

            // ========== 7. 插入平台商家收入记录（类型 6） ==========
            WalletTransaction merchantTrans = new WalletTransaction();
            merchantTrans.setTransactionType(6); // 商家收入
            merchantTrans.setAmount(refundAmount);
            merchantTrans.setTransactionTime(new Date());
            merchantTrans.setStatus(1);
            merchantTrans.setCreateTime(new Date());
            merchantTrans.setUpdateTime(new Date());
            merchantTrans.setOutTradeNo(oldOrder.getOrderNumber() + "_merchant");
            walletTransactionMapper.insert(merchantTrans);

            // ========== 8. 更新订单中的平台收入字段 ==========
            oldOrder.setMerchantIncome(merchantIncome);
            oldOrder.setStatus(6); // 同意退款
            oldOrder.setUpdateTime(new Date());
            orderMapper.updateById(oldOrder);

            return 1;
        }

        // 非退款操作：仅更新状态
        Order order = new Order();
        BeanUtils.copyProperties(orderDTO, order);
        return orderMapper.updateById(order);
    }


}
