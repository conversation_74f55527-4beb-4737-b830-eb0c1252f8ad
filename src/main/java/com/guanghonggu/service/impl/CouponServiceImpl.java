package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.service.CouponService;
import com.guanghonggu.dto.CouponCreateDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Coupon;
import com.guanghonggu.mapper.CouponMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class CouponServiceImpl extends ServiceImpl<CouponMapper, Coupon> implements CouponService {
    @Autowired
    private CouponMapper couponMapper;

    /**
     * 查询优惠券
     */
    @Override
    public Page<Coupon> getAllCoupon(Integer page, Integer size,Integer isDelete){
        Page<Coupon> couponPage = new Page<>(page, size);
        QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("is_delete", 0);
        return couponMapper.selectPage(couponPage, queryWrapper);

    }

    /**
     * 创建优惠券
     */
    @Override
    public ResultDTO<String> postCoupon(CouponCreateDTO couponCreateDTO){
        Coupon coupon = new Coupon();
        BeanUtils.copyProperties(couponCreateDTO, coupon);

        coupon.setRemainingQuantity(couponCreateDTO.getQuantity());
        coupon.setCreateTime(new Date());
        coupon.setUpdateTime(new Date());
        coupon.setIsDelete(0);
        this.save(coupon);
        return ResultDTO.success("优惠券创建成功",null);
    }


    /**
     * 编辑优惠券
     */
    @Override
    public int updateCoupon(CouponCreateDTO couponCreateDTO){
        QueryWrapper<Coupon> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("coupon_id", couponCreateDTO.getCouponId());
        Coupon coupon = new Coupon();
        BeanUtils.copyProperties(couponCreateDTO, coupon);
        return couponMapper.update(coupon, queryWrapper);
    }

    /**
     * 删除优惠券
     */
    @Override
    public int deleteCoupon(CouponCreateDTO couponCreateDTO){
        LambdaUpdateWrapper<Coupon> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Coupon::getCouponId, couponCreateDTO.getCouponId())
                .set(Coupon::getIsDelete, 1);
        return couponMapper.update(null, updateWrapper);
    }
}
