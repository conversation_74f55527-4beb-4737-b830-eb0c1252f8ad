package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.CaregiverServiceTypeDTO;
import com.guanghonggu.entity.CaregiverServiceType;
import com.guanghonggu.mapper.CaregiverServiceTypeMapper;
import com.guanghonggu.service.CaregiverServiceTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class CaregiverServiceTypeServiceImpl extends ServiceImpl<CaregiverServiceTypeMapper, CaregiverServiceType>
        implements CaregiverServiceTypeService {

    @Autowired
    private CaregiverServiceTypeMapper caregiverServiceTypeMapper;

    @Override
    public List<CaregiverServiceType> queryPermissionCaregiverServiceType(CaregiverServiceTypeDTO caregiverServiceTypeDTO){
        LambdaQueryWrapper<CaregiverServiceType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CaregiverServiceType::getCaregiverId, caregiverServiceTypeDTO.getCaregiverId());
        return caregiverServiceTypeMapper.selectList(queryWrapper);
    }

    @Override
    public void addCaregiverServiceTypes(List<CaregiverServiceType> caregiverServiceTypes) {
        caregiverServiceTypeMapper.insertBatch(caregiverServiceTypes);
    }

    @Override
    public long deletePermissionCaregiverServiceType(CaregiverServiceTypeDTO caregiverServiceTypeDTO){
        LambdaQueryWrapper<CaregiverServiceType> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CaregiverServiceType::getId, caregiverServiceTypeDTO.getId());
        return caregiverServiceTypeMapper.delete(queryWrapper);
    }


}
