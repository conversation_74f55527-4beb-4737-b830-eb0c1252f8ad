package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.service.SysConfigService;
import com.guanghonggu.dto.SysConfigDTO;
import com.guanghonggu.entity.SysConfig;
import com.guanghonggu.mapper.SysConfigMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Service
public class SysConfigServiceImpl implements SysConfigService {
    @Autowired
    private SysConfigMapper sysConfigMapper;

    @Override
    public int addSysConfig(SysConfigDTO sysConfigDTO){
        SysConfig sysConfig = new SysConfig();
        BeanUtils.copyProperties(sysConfigDTO, sysConfig);
        sysConfig.setCreateTime(new Date());
        return sysConfigMapper.insert(sysConfig);
    }

    @Override
    public int updateSysConfig(SysConfigDTO sysConfigDTO){
        QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", sysConfigDTO.getId());
        SysConfig sysConfig = sysConfigMapper.selectOne(queryWrapper);
        BeanUtils.copyProperties(sysConfigDTO, sysConfig);
        return sysConfigMapper.updateById(sysConfig);
    }

    @Override
    public List<SysConfig> getIdSysConfig(SysConfigDTO sysConfigDTO){
        QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", sysConfigDTO.getId());
        return sysConfigMapper.selectList(queryWrapper);
    }

    @Override
    public Page<SysConfig> getAllSysConfig(Integer page, Integer size){
        Page<SysConfig> sysConfigPage = new Page<>(page, size);
        return sysConfigMapper.selectPage(sysConfigPage, null);
    }

    @Override
    public int deleteSysConfig(SysConfigDTO sysConfigDTO){
        QueryWrapper<SysConfig> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", sysConfigDTO.getId());
        return sysConfigMapper.delete(queryWrapper);
    }

    /**
     * 获取抽成比例
     */
    @Override
    public BigDecimal getPlatformRatio() {
        LambdaQueryWrapper<SysConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysConfig::getConfigKey, "platform_commission_ratio");

        SysConfig config = sysConfigMapper.selectOne(wrapper);
        if (config == null) {
            throw new RuntimeException("平台抽成比例配置缺失");
        }

        try {
            return new BigDecimal(config.getConfigValue());
        } catch (NumberFormatException e) {
            throw new RuntimeException("平台抽成比例格式错误，请联系管理员");
        }
    }

    /**
     * 根据配置查询抽成比例
     */
    @Override
    public BigDecimal getRatioByKey(String key) {
        LambdaQueryWrapper<SysConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysConfig::getConfigKey, key);
        SysConfig config = sysConfigMapper.selectOne(wrapper);

        if (config == null) {
            throw new RuntimeException("配置项 [" + key + "] 不存在");
        }

        try {
            return new BigDecimal(config.getConfigValue());
        } catch (NumberFormatException e) {
            throw new RuntimeException("配置项 [" + key + "] 的值格式错误：" + config.getConfigValue());
        }
    }

}
