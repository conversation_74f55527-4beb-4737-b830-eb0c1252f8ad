package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.UserVipDTO;
import com.guanghonggu.entity.UserVip;
import com.guanghonggu.mapper.UserVipMapper;
import com.guanghonggu.service.UserVipService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserVipServiceImpl extends ServiceImpl<UserVipMapper, UserVip> implements UserVipService {
    @Autowired
    private UserVipMapper userVipMapper;

    @Override
    public List<UserVip> getUserVip(){
        return userVipMapper.getUserVipWithCoupon();
    }

    @Override
    public UserVip getUserVipByVipId(UserVipDTO userVipDTO){
        LambdaQueryWrapper<UserVip> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserVip::getId, userVipDTO.getId());
        return userVipMapper.selectOne(queryWrapper);
    }

    @Override
    public int addUserVip(UserVipDTO userVipDTO) {
        UserVip userVip = new UserVip();
        BeanUtils.copyProperties(userVipDTO, userVip);
        return userVipMapper.insert(userVip);
    }

    @Override
    public int updateUserVip(UserVipDTO userVipDTO){
        LambdaUpdateWrapper<UserVip> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(UserVip::getId, userVipDTO.getId());
        if (userVipDTO.getRechargeBalance() != null) {
            updateWrapper.set(UserVip::getRechargeBalance, userVipDTO.getRechargeBalance());
        }
        if (userVipDTO.getCouponId() != null) {
            updateWrapper.set(UserVip::getCouponId, userVipDTO.getCouponId());
        }
        if (userVipDTO.getIssuedCount() != null) {
            updateWrapper.set(UserVip::getIssuedCount, userVipDTO.getIssuedCount());
        }
        return userVipMapper.update(null, updateWrapper);
    }

    @Override
    public int deleteUserVip(UserVipDTO userVipDTO) {
        LambdaQueryWrapper<UserVip> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserVip::getId, userVipDTO.getId());
        return userVipMapper.delete(queryWrapper);
    }
}
