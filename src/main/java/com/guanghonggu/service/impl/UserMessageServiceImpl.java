package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserMessageDTO;
import com.guanghonggu.entity.Caregiver;
import com.guanghonggu.entity.User;
import com.guanghonggu.entity.UserMessage;
import com.guanghonggu.mapper.CaregiverMapper;
import com.guanghonggu.mapper.MessageMapper;
import com.guanghonggu.mapper.UserMapper;
import com.guanghonggu.mapper.UserMessageMapper;
import com.guanghonggu.service.UserMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

@Service
public class UserMessageServiceImpl extends ServiceImpl<UserMessageMapper, UserMessage> implements UserMessageService {

    @Autowired
    private UserMapper userMapper; // 用户表 Mapper
    @Autowired
    private CaregiverMapper caregiverMapper; // 阿姨表 Mapper
    @Autowired
    private UserMessageMapper userMessageMapper; // 用户消息表 Mapper
    @Autowired
    private MessageMapper messageMapper; // 消息表 Mapper

    @Override
    public ResultDTO<?> sendNotificationToRecipients(Long messageId, Integer userType) {
        try {
            List<UserMessageDTO> userMessages = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now(); // 获取当前时间

            if (userType == 1) {
                // 1. 查询所有用户
                List<User> users = userMapper.selectList(new QueryWrapper<User>());
                if (users.isEmpty()) {
                    return ResultDTO.error("没有用户数据");
                }

                // 2. 构建用户消息记录
                for (User user : users) {
                    UserMessageDTO userMessageDTO = new UserMessageDTO();
                    userMessageDTO.setUserId(user.getUserId());
                    userMessageDTO.setUserType(1); // 1 表示用户
                    userMessageDTO.setMessageId(messageId);
                    userMessageDTO.setIsRead(0); // 默认未读
                    userMessageDTO.setReadTime(null);
                    userMessageDTO.setCreateTime(now);

                    userMessages.add(userMessageDTO);
                }

            } else if (userType == 2) {
                // 1. 查询所有阿姨
                List<Caregiver> caregivers = caregiverMapper.selectList(new QueryWrapper<Caregiver>());
                if (caregivers.isEmpty()) {
                    return ResultDTO.error("没有阿姨数据");
                }

                // 2. 构建阿姨消息记录
                for (Caregiver caregiver : caregivers) {
                    UserMessageDTO userMessageDTO = new UserMessageDTO();
                    userMessageDTO.setUserId(caregiver.getCaregiverId());
                    userMessageDTO.setUserType(2); // 2 表示阿姨
                    userMessageDTO.setMessageId(messageId);
                    userMessageDTO.setIsRead(0); // 默认未读
                    userMessageDTO.setReadTime(null);
                    userMessageDTO.setCreateTime(now);

                    userMessages.add(userMessageDTO);
                }
            } else {
                return ResultDTO.error("无效的用户类型");
            }

            // 3. 批量插入消息记录到 user_message 表
            userMessageMapper.insertBatch(userMessages);

            // 4. 返回成功的结果
            return ResultDTO.success("消息已成功下发", userMessages);

        } catch (Exception e) {
            // 异常处理
            return ResultDTO.error("消息下发失败: " + e.getMessage());
        }
    }
}