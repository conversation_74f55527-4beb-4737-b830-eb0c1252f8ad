package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.CleaningItemShopOrderDTO;
import com.guanghonggu.entity.CleaningItemShopOrder;
import com.guanghonggu.mapper.CleaningItemShopOrderMapper;
import com.guanghonggu.service.CleaningItemShopOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CleaningItemShopOrderServiceImpl extends ServiceImpl<CleaningItemShopOrderMapper, CleaningItemShopOrder>
        implements CleaningItemShopOrderService {
    @Autowired
    private CleaningItemShopOrderMapper cleaningItemShopOrderMapper;

    @Override
    public Page<CleaningItemShopOrder> getAllCleaningItemShopOrder(Integer page, Integer size, CleaningItemShopOrderDTO cleaningItemShopOrderDTO){
        Page<CleaningItemShopOrder> pages = new Page<>(page, size);
        return cleaningItemShopOrderMapper.selectPage(pages, null);
    }

    @Override
    public CleaningItemShopOrder getCleaningItemShopOrder(CleaningItemShopOrderDTO cleaningItemShopOrderDTO){
        LambdaUpdateWrapper<CleaningItemShopOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CleaningItemShopOrder::getId, cleaningItemShopOrderDTO.getId())
                .set(CleaningItemShopOrder::getView, 1);
        cleaningItemShopOrderMapper.update(null, updateWrapper);
        LambdaQueryWrapper<CleaningItemShopOrder> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(CleaningItemShopOrder::getId, cleaningItemShopOrderDTO.getId());
        return cleaningItemShopOrderMapper.selectOne(queryWrapper);
    }

    @Override
    public Integer modifyOrderStatus(CleaningItemShopOrderDTO cleaningItemShopOrderDTO){
        LambdaUpdateWrapper<CleaningItemShopOrder> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(CleaningItemShopOrder::getId, cleaningItemShopOrderDTO.getId())
                .set(CleaningItemShopOrder::getStatus, cleaningItemShopOrderDTO.getStatus());
        return cleaningItemShopOrderMapper.update(null, updateWrapper);
    }
}
