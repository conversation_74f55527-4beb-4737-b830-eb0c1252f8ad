package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.service.MessageService;
import com.guanghonggu.dto.MessageDTO;
import com.guanghonggu.entity.Message;
import com.guanghonggu.mapper.MessageMapper;
import com.guanghonggu.mapper.RefundRejectReasonMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class MessageServiceImpl implements MessageService {

    @Autowired
    private MessageMapper messageMapper;
    @Autowired
    private RefundRejectReasonMapper refundRejectReasonMapper;

    @Override
    public Page<Message> getAllMessage(Integer page, Integer size){
        Page<Message> pageMessage = new Page<>(page, size);
        return messageMapper.selectPage(pageMessage, null);
    }

    @Override
    public int addMessage(MessageDTO messageDTO){
        Message test = new Message();
        BeanUtils.copyProperties(messageDTO,test);
        return messageMapper.insert(test);
    }

    @Override
    public int updateMessage(MessageDTO messageDTO){
        QueryWrapper<Message> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id",messageDTO.getId());
        Message updateMessage = new Message();
        BeanUtils.copyProperties(messageDTO,updateMessage);
        return messageMapper.update(updateMessage,queryWrapper);
    }

    @Override
    public int deleteMessage(MessageDTO messageDTO){
        QueryWrapper<Message> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id",messageDTO.getId());
        return messageMapper.delete(queryWrapper);
    }

}
