package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.UserDTO;
import com.guanghonggu.mapper.UserMapper;
import com.guanghonggu.service.UserService;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.StatisticsDTO;
import com.guanghonggu.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User>
        implements UserService {
    @Autowired
    private UserMapper userMapper;

    @Override
    public Page<User> getAllUser(Integer page, Integer size){
        Page<User> userPage = new Page<>(page, size);
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        return userMapper.selectPage(userPage, queryWrapper);
    }

    /**
     * 根据用户id查询
     */
    @Override
    public List<User> getUserId(UserDTO userDTO){
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userDTO.getUserId());
        return userMapper.selectList(queryWrapper);
    }

}
