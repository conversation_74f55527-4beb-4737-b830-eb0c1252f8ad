package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.guanghonggu.dto.GiftCouponProbabilityDTO;
import com.guanghonggu.entity.GiftCouponProbability;
import com.guanghonggu.mapper.GiftCouponProbabilityMapper;
import com.guanghonggu.service.GiftCouponProbabilityService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class GiftCouponProbabilityServiceImpl extends ServiceImpl<GiftCouponProbabilityMapper, GiftCouponProbability>
        implements GiftCouponProbabilityService {

    @Autowired
    private GiftCouponProbabilityMapper giftCouponProbabilityMapper;

    @Override
    public List<GiftCouponProbability> getGiftCouponProbability(){
        return giftCouponProbabilityMapper.getGiftCouponProbabilityWithCoupon();
    }

    @Override
    public GiftCouponProbability getGiftCouponProbabilityById(GiftCouponProbabilityDTO giftCouponProbabilityDTO){
        LambdaQueryWrapper<GiftCouponProbability> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GiftCouponProbability::getId, giftCouponProbabilityDTO.getId());
        return giftCouponProbabilityMapper.selectOne(queryWrapper);
    }

    @Override
    public int addGiftCouponProbability(GiftCouponProbabilityDTO giftCouponProbabilityDTO){
        GiftCouponProbability giftCouponProbability = new GiftCouponProbability();
        BeanUtils.copyProperties(giftCouponProbabilityDTO, giftCouponProbability);
        return giftCouponProbabilityMapper.insert(giftCouponProbability);
    }

    @Override
    public int updateGiftCouponProbability(GiftCouponProbabilityDTO giftCouponProbabilityDTO){
        LambdaUpdateWrapper<GiftCouponProbability> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(GiftCouponProbability::getId, giftCouponProbabilityDTO.getId())
                .set(GiftCouponProbability::getReleaseProbability, giftCouponProbabilityDTO.getReleaseProbability());
        return giftCouponProbabilityMapper.update(null, updateWrapper);
    }

    @Override
    public int deleteGiftCouponProbability(GiftCouponProbabilityDTO giftCouponProbabilityDTO){
        LambdaQueryWrapper<GiftCouponProbability> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(GiftCouponProbability::getId, giftCouponProbabilityDTO.getId());
        return giftCouponProbabilityMapper.delete(queryWrapper);
    }
}
