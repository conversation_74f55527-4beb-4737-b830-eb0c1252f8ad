package com.guanghonggu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.guanghonggu.service.SysPictureService;
import com.guanghonggu.dto.SysPictureDTO;
import com.guanghonggu.entity.SysPicture;
import com.guanghonggu.mapper.SysPictureMapper;
import com.guanghonggu.util.AliyunOssUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Service
public class SysPictureServiceImpl implements SysPictureService {

    @Autowired
    private SysPictureMapper sysPictureMapper;

    @Autowired
    private AliyunOssUtil aliyunOssUtil;

    /**
     * 查询所有系统图片，并生成临时签名地址
     */
    @Override
    public List<SysPicture> getAllSysPicture(SysPictureDTO dto) {
        List<SysPicture> list = sysPictureMapper.selectList(new LambdaQueryWrapper<>());

        for (SysPicture pic : list) {
            String fullPath = pic.getDownloadAddress();
            if (fullPath == null) continue;

            String objectName;

            // 如果是完整链接，提取出 objectName
            if (fullPath.startsWith("http")) {
                String[] parts = fullPath.split(".com/", 2);
                objectName = parts.length == 2 ? parts[1] : null;
            } else {
                objectName = fullPath;
            }

            if (objectName != null) {
                String signedUrl = aliyunOssUtil.generatePresignedUrl(objectName);
                pic.setSignedUrl(signedUrl);
            }
        }

        return list;
    }

    /**
     * 上传图片并保存记录
     */
    @Override
    public void uploadPicture(SysPictureDTO sysPictureDTO, MultipartFile file) {
        // 上传文件到 OSS
        String ossUrl = aliyunOssUtil.upload(file);
        if (ossUrl == null) {
            throw new RuntimeException("上传 OSS 失败");
        }

        // 构造 SysPicture 实体
        SysPicture picture = new SysPicture();
        BeanUtils.copyProperties(sysPictureDTO, picture);
        picture.setDownloadAddress(ossUrl);

        // 插入数据库
        sysPictureMapper.insert(picture);
    }

    /**
     * 单张图片生成签名链接
     */
    @Override
    public String generatePreviewUrl(Long pictureId) {
        SysPicture picture = sysPictureMapper.selectById(pictureId);
        if (picture == null || picture.getDownloadAddress() == null) {
            throw new RuntimeException("图片不存在");
        }

        // 提取 objectName
        String objectName = aliyunOssUtil.extractObjectName(picture.getDownloadAddress());
        return aliyunOssUtil.generatePresignedUrl(objectName);
    }

    /**
     * 删除 OSS 和数据库图片记录
     */
    @Override
    public long deleteSysPicture(SysPictureDTO sysPictureDTO) {
        Long pictureId = sysPictureDTO.getPictureId();
        if (pictureId == null) {
            throw new RuntimeException("图片id不能为空");
        }

        SysPicture picture = sysPictureMapper.selectById(pictureId);
        if (picture == null) {
            throw new RuntimeException("要删除的图片不存在");
        }

        String fullUrl = picture.getDownloadAddress();
        if (fullUrl != null) {
            aliyunOssUtil.delete(fullUrl); // 自动提取 objectName 删除
        }

        return sysPictureMapper.deleteById(pictureId);
    }

    /**
     * 修改图片信息（含可选替换文件）
     */
    @Override
    public void updatePicture(SysPictureDTO sysPictureDTO, MultipartFile file) {
        System.out.println("Service接收到的 sysPictureDTO: " + sysPictureDTO);
        System.out.println("pictureId: " + (sysPictureDTO != null ? sysPictureDTO.getPictureId() : "sysPictureDTO is null"));

        Long pictureId = sysPictureDTO.getPictureId();
        if (pictureId == null) throw new RuntimeException("图片 ID 不能为空");

        SysPicture picture = sysPictureMapper.selectById(pictureId);
        if (picture == null) throw new RuntimeException("要修改的图片不存在");

        // 替换图片文件（如果传了新文件）
        if (file != null && !file.isEmpty()) {
            // 删除旧文件
            if (picture.getDownloadAddress() != null) {
                aliyunOssUtil.delete(picture.getDownloadAddress());
            }
            // 上传新文件
            String newUrl = aliyunOssUtil.upload(file);
            picture.setDownloadAddress(newUrl);
        }

        // 更新其他字段
        picture.setPictureName(sysPictureDTO.getPictureName());
        picture.setPictureType(sysPictureDTO.getPictureType());
        picture.setJumpLink(sysPictureDTO.getJumpLink());

        sysPictureMapper.updateById(picture);
    }
}
