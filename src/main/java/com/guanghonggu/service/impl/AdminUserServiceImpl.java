package com.guanghonggu.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.guanghonggu.service.AdminUserService;
import com.guanghonggu.dto.AdminUserDTO;
import com.guanghonggu.entity.AdminUser;
import com.guanghonggu.mapper.AdminUserMapper;
import com.guanghonggu.util.JwtUtil;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

@Service
public class AdminUserServiceImpl implements AdminUserService {
    @Autowired
    private AdminUserMapper adminUserMapper;

    @Override
    public Map<String, Object> login(AdminUserDTO dto) {
        // 从数据库中查询用户信息
        AdminUser user = adminUserMapper.selectOne(
                new QueryWrapper<AdminUser>().eq("username", dto.getUsername())
        );

        // 如果用户不存在或密码错误，则抛出异常
        if (user == null || !user.getPassword().equals(dto.getPassword())) {
            throw new RuntimeException("用户名或密码错误");
        }

        // 使用 Sa-Token 登录并生成 token
        StpUtil.login(user.getId());

        // 返回 Sa-Token 标准格式，包含用户ID
        Map<String, Object> result = new HashMap<>();
        result.put("tokenName", StpUtil.getTokenName());
        result.put("tokenValue", StpUtil.getTokenValue());
        result.put("accountPermission", user.getAccountPermission());
        result.put("id", user.getId()); // 添加用户ID

        return result;
    }

    @Override
    public String addAccount(AdminUserDTO adminUserDTO) {
        try {

            // 1. 先检查用户名是否已存在
            AdminUser existingUser = adminUserMapper.selectOne(
                    new QueryWrapper<AdminUser>().eq("username", adminUserDTO.getUsername())
            );

            if (existingUser != null) {
                throw new RuntimeException("用户名已存在，请选择其他用户名");
            }

            // 2. 将 DTO 转换为实体类
            AdminUser adminUser = new AdminUser();
            BeanUtils.copyProperties(adminUserDTO, adminUser);

            // 3. 设置默认值（如果前端没有传递）
            if (adminUser.getStatus() == null) {
                adminUser.setStatus(1); // 默认状态为启用
            }
            if (adminUser.getAccountPermission() == null) {
                adminUser.setAccountPermission(1); // 默认权限为员工
            }
            if (adminUser.getNickname() == null) {
                adminUser.setNickname(" "); // 如果没有昵称，则设置为空格
            }

            // 设置时间字段
            Date now = new Date();
            adminUser.setCreateTime(now);
            adminUser.setUpdateTime(now);

            // 4. 将用户信息保存到数据库
            int result = adminUserMapper.insert(adminUser);  // 使用 insert 而不是 updateById

            if (result > 0) {
                return "用户注册成功";
            } else {
                throw new RuntimeException("用户注册失败");
            }

        } catch (RuntimeException e) {
            // 重新抛出运行时异常
            throw new RuntimeException("注册失败: " + e.getMessage(), e);  // 包装异常并传递
        } catch (Exception e) {
            // 捕获其他异常并包装
            throw new RuntimeException("注册失败: " + e.getMessage(), e);
        }
    }

}

