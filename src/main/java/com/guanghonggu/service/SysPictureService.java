package com.guanghonggu.service;

import com.guanghonggu.dto.SysPictureDTO;
import com.guanghonggu.entity.SysPicture;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

public interface SysPictureService {

    /**
     * 查询首页图片
     */
    List<SysPicture> getAllSysPicture(SysPictureDTO sysPictureDTO);

    /**
     * 上传首页图片
     */
    void uploadPicture(SysPictureDTO sysPictureDTO, MultipartFile file);

    /**
     * 生成临时链接
     */
    String generatePreviewUrl(Long pictureId);

    /**
     * 删除首页图片
     */
    long deleteSysPicture(SysPictureDTO sysPictureDTO);

    /**
     * 修改首页图片
     */
    void updatePicture(SysPictureDTO sysPictureDTO, MultipartFile file);

}
