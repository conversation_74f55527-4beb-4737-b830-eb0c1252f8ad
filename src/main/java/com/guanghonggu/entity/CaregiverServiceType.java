package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.util.Date;

@Data
public class CaregiverServiceType {

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @TableField(value = "caregiver_id")
    private Long caregiverId;

    @TableField(value = "service_type_id")
    private Integer serviceTypeId;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;

    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

}
