package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
@TableName(value = "wallet")
public class Wallet {
    /**
     * 钱包id
     */
    @TableId(value = "wallet_id",type = IdType.AUTO)
    private Long walletId;

    /**
     * 用户/阿姨id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 用户类型
     */
    @TableField(value = "user_type")
    private Integer userType;

    /**
     * 余额
     */
    @TableField(value = "balance")
    private BigDecimal balance;

    /**
     * 储值金额（无法提现）
     */
    @TableField(value = "recharge_balance")
    private BigDecimal rechargeBalance;

    /**
     * 总收入
     */
    @TableField(value = "total_income")
    private BigDecimal totalIncome;

    /**
     * 总支出
     */
    @TableField(value = "total_spent")
    private BigDecimal totalSpent;

//    /**
//     * 创建时间
//     */
//    @TableField(value = "creat_time",fill = FieldFill.INSERT)
//    private Date creatTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time",fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;



}
