package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@TableName(value = "gift_coupon_probability")
@Data
public class GiftCouponProbability {
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField(value = "release_probability")
    private Double releaseProbability;

    @TableField(value = "coupon_id")
    private Long couponId;

    @TableField(exist = false)
    private String couponTitle;

    @TableField(exist = false)
    private String couponDescription;
}
