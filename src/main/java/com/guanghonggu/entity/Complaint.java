package com.guanghonggu.entity;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 投诉表
 * @TableName complaint
 */
@TableName(value ="complaint")
@Data
public class Complaint {
    /**
     * 投诉id
     */
    @TableId(value = "complaint_id", type = IdType.AUTO)
    private Long complaintId;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 阿姨id
     */
    @TableField(value = "caregiver_id")
    private Long caregiverId;

    /**
     * 投诉状态 (0: 待处理, 1: 处理中, 2: 已解决)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 投诉类型 (1: 服务质量, 2: 人员态度, 3: 订单问题, 其他自定义类型)
     */
    @TableField(value = "complaint_type")
    private Integer complaintType;

    /**
     * 投诉内容
     */
    @TableField(value = "complaint_detail")
    private String complaintDetail;

    /**
     * 反馈
     */
    @TableField(value = "feedback")
    private String feedback;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    private String caregiverName;
    private String userName;

}