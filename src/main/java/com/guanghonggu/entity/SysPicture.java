package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

@Data
@TableName(value = "sys_picture")
public class SysPicture {
    /**
     * 图片id
     */
    @TableId(value = "picture_id",type = IdType.AUTO)
    private Long pictureId;

    /**
     * 图片名称
     */
    @TableField(value = "picture_name")
    private String pictureName;

    /**
     * 图片类型
     */
    @TableField(value = "picture_type")
    private Integer pictureType;

    /**
     * 图片url地址
     */
    @TableField(value = "download_address")
    private String downloadAddress;

    /**
     * 跳转链接
     */
    @TableField(value = "jump_link")
    private String jumpLink;

    /**
     * 图片临时地址（不存入数据库）
     */
    @TableField(exist = false)
    private String signedUrl;

}
