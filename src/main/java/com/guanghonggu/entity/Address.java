// Address.java 实体类整理优化版（字段排序+注释）
package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.Value;

import java.util.Date;

/**
 * 地址表实体类
 * 对应数据库中的 address 表
 */
@TableName(value = "address")
@Data

public class Address {

    // ========== 主键与用户字段 ==========

    /** 地址ID（主键） */
    @TableId(value = "address_id", type = IdType.AUTO)
    private Long addressId;

    /** 用户ID（可用于用户或阿姨） */
    @TableField(value = "user_id")
    private Long userId;

    /** 用户姓名（可用于用户或阿姨） */
    @TableField(value = "user_name")
    private String userName;

    /** 用户类型（1：用户，2：阿姨） */
    @TableField(value = "user_type")
    private Integer userType;

    /** 用户手机号（可用于用户或阿姨） */
    @TableField(value = "phone_number")
    private String phone;

    /** 经纬度 */
    @TableField(exist = false)
    @JsonProperty("longitudeAndLatitude")
    private String longitudeAndLatitude;

    // ========== 区域字段 ==========

    /** 详细街道 */
    @TableField(value = "location_details")
    private String locationDetails;

    /** 区县 */
    @TableField(value = "area")
    private String area;

    /** 建筑物 */
    @TableField(value = "place_name")
    private String placeName;

    // ========== 地址内容 ==========

    /** 详细地址 */
    @TableField(value = "detailed_address")
    private String detailedAddress;

    /** 经度 */
    @TableField(value = "longitude")
    private String longitude;

    /** 纬度 */
    @TableField(value = "latitude")
    private String latitude;

    /** 是否默认地址（0 否，1 是） */
    @TableField(value = "is_default")
    private Integer isDefault;

    // ========== 时间字段 ==========

    /** 创建时间（自动填充） */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /** 更新时间（自动填充） */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /** 最近下单时间（用于删除默认地址后的替代） */
    @TableField(value = "recent_order_time")
    private Date recentOrderTime;




}
