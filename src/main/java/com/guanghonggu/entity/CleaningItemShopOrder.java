package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

@Data
public class CleaningItemShopOrder {
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @TableField(value = "order_number")
    private String orderNumber;

    @TableField(value = "caregiver_id")
    private Long caregiverId;

    @TableField(value = "total_price")
    private Double totalPrice;

    @TableField(value = "address_id")
    private Long addressId;

    @TableField(value = "status")
    private Integer status;

    @TableField(value = "payment_method")
    private Integer paymentMethod;

    @TableField(value = "pay_order_number")
    private String payOrderNumber;

    @TableField(value = "pay_time")
    private String payTime;

    @TableField(value = "remark")
    private String remark;

    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private String createTime;

    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private String updateTime;

    @TableField(value = "view")
    private Integer view;
}
