package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

@Data
@TableName(value = "refund_reject_reason")
public class RefundRejectReason {
    /**
     * id
     */
    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 退款订单id
     */
    @TableField(value = "refund_id")
    private Long refundId;

    /**
     * 阿姨id
     */
    @TableField(value = "caregiver_id")
    private Long caregiverId;

    /**
     * 拒绝理由
     */
    @TableField(value = "reject_reason")
    private String rejectReason;

    /**
     * 创建时间
     */
    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private Date createTime;

}
