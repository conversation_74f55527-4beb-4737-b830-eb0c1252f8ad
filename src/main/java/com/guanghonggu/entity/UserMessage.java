package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Date;

@TableName(value = "user_message")
@Data
public class UserMessage {

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    @TableField(value = "user_id")
    private Long userId;

    @TableField(value = "user_type")
    private Integer userType;

    @TableField(value = "message_id")
    private Long messageId;

    @TableField(value = "is_read")
    private Integer isRead;

    @TableField(value = "read_time")
    private Date readTime;

    @TableField(value = "create_time",fill = FieldFill.INSERT)
    private LocalDateTime createTime;
}
