package com.guanghonggu.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

@TableName(value = "`order`")
@Data
public class Order {
    /**
     * 订单id
     */
    @TableId(value = "order_id", type = IdType.AUTO)
    private Long orderId;

    /**
     * 订单编号
     */
    @TableField(value = "order_number")
    private String orderNumber;

    /**
     * 用户id
     */
    @TableField(value = "user_id")
    private Long userId;

    /**
     * 阿姨id
     */
    @TableField(value = "caregivers_id")
    private Long caregiversId;

    /**
     * 地址id
     */
    @TableField(value = "address_id")
    private Long addressId;

    /**
     * 订单状态 (0: 等待接单，1: 已预约, 2: 进行中, 3: 订单结束, 4: 已取消支付，5：退款中，6：已退款，7：待支付，8：服务完成)
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 服务类型Id --（1：日常保洁，2：开荒保洁，3：玻璃清洗，4：机器拆洗，5：企业保洁）
     */
    @TableField(value = "service_type_id")
    private Long serviceTypeId;

    /**
     * 预约时间
     */
    @TableField(value = "appointment_time")
    private Date appointmentTime;

    /**
     * 订单完成时间
     */
    @TableField(value = "order_completion_time")
    private Date orderCompletionTime;

    /**
     * 服务结束时间
     */
    @TableField(value = "service_end_time")
    private Date serviceEndTime;

    /**
     * 订单金额
     */
    @TableField(value = "total_price")
    private BigDecimal totalPrice;

    /**
     * 优惠金额
     */
    @TableField(value = "preferential_price")
    private BigDecimal preferentialPrice;

    /**
     * 实际付款金额
     */
    @TableField(value = "actual_payment_price")
    private BigDecimal actualPaymentPrice;

    /**
     * 支付类型（1：WechatPay：微信，2：AliPay：支付宝，3：WalletPay：钱包）
     */
    @TableField(value = "pay_methods")
    private Integer payMethods;

    /**
     * 支付时间
     */
    @TableField(value = "pay_time")
    private Date payTime;

    /**
     * 支付订单号
     */
    @TableField(value = "pay_order_number")
    private String payOrderNumber;

    /**
     * 优惠券id
     */
    @TableField(value = "coupon_id")
    private Long couponId;

    /**
     * 订单类型（1：立即分配，2：预约大厅，3：预约阿姨）
     */
    @TableField(value = "order_type")
    private Integer orderType;

    /**
     * 创建时间
     */
    @TableField(value = "create_time", fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField(value = "update_time", fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 阿姨收入
     */
    @TableField("caregiver_income")
    private BigDecimal caregiverIncome;

    /**
     * 商家收入
     */
    @TableField("merchant_income")
    private BigDecimal merchantIncome;

    /**
     * 对应 service_type 表中的 name 字段（非数据库字段）
     */
    @TableField(exist = false)
    private String serviceTypeName;

//    /**
//     * 备注（用于记录部分退款说明）
//     */
//    @TableField(value = "remark")
//    private String remark;

    private String caregiverName;
    private String userName;

}

