package com.guanghonggu.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class CouponCreateDTO {
    private Long couponId;            // 优惠券ID
    private String title;             // 优惠券标题
    private String description;       // 描述
    private Integer discountType;     //折扣类型（1：固定金额，2：百分比）
    private Integer couponType;       // 优惠券类型(0:全部，1-5:对应保洁类型)
    private Integer distributionMethod; // 发放方式（1：下单赠送，2：注册赠送，3：充值赠送，4：活动领取）
    private BigDecimal fullAmount;    // 满多少
    private BigDecimal discountValue; // 减多少
    private BigDecimal maxDiscount;   // 最大抵扣（百分比券用）
    private Integer validDay;         // 有效天数（可选）
    private Integer limitCount;       // 限领次数

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validStartTime;      // 使用开始时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date validEndTime;        // 使用结束时间

    private Integer quantity;         // 总数量

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date collectStartTime;    // 领取开始时间

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date collectEndTime;      // 领取结束时间

    /**
     * 分页（开始的页数）
     */
    private Integer page;

    /**
     *分页（一页有多少信息）
     */
    private Integer size;

    /**
     * 默认使用
     */
    private Integer isDefaultUse;

    /**
     * 是否删除
     */
    private Integer isDelete;
}
