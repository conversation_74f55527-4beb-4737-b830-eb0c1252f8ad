package com.guanghonggu.dto;

import lombok.Data;

import java.util.Date;

/**
 * 投诉表
 */
@Data
public class ComplaintDTO {
    /**
     * 投诉id
     */
    private Long complaintId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 阿姨id
     */
    private Long caregiverId;

    /**
     * 投诉状态 (0: 待处理, 1: 处理中, 2: 已解决)
     */
    private Integer status;

    /**
     * 投诉类型 (1: 服务质量, 2: 人员态度, 3: 订单问题, 其他自定义类型)
     */
    private Integer complaintType;

    /**
     * 投诉内容
     */
    private String complaintDetail;

    /**
     * 反馈
     */
    private String feedback;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 分页（开始的页数）
     */
    private Integer page;

    /**
     *分页（一页有多少信息）
     */
    private Integer size;


}