package com.guanghonggu.dto;

import com.guanghonggu.constant.HttpStatusConstants;
import lombok.Data;

@Data
public class ResultDTO<T> {

    private int code;        // 响应状态码

    private String message;  // 响应消息

    private T data;          // 响应数据

    // 构造方法
    public ResultDTO(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public ResultDTO(int code, String message) {
        this.code = code;
        this.message = message;
    }

    // 成功响应
    public static <T> ResultDTO<T> success(String message, T data) {
        return new ResultDTO<>(HttpStatusConstants.SUCCESS, message, data);
    }

    public static <T> ResultDTO<T> notDataSuccess(String message) {
        return new ResultDTO<>(HttpStatusConstants.SUCCESS, message);
    }

    // 失败响应
    public static <T> ResultDTO<T> error(String message) {
        return new ResultDTO<>(HttpStatusConstants.ERROR, message, null);
    }

}