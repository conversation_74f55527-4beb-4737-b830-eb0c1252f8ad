package com.guanghonggu.dto;

import lombok.Data;

import java.util.Date;

@Data
public class UserCouponDTO {
    /**
     * 用户优惠卷id
     */
    private Long userCouponId;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 优惠卷id
     */
    private Long couponId;

    /**
     * 使用的订单编号
     */
    private Long orderId;

    /**
     * 获取时间
     */
    private Date acquireTime;

    /**
     * 使用时间
     */
    private Date usedTime;

    /**
     * 到期时间
     */
    private Date expirationTime;

    /**
     * 状态（0：不可用，1：未使用，2：已使用）
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

}
