package com.guanghonggu.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class StatisticsDTO {
    /**
     * 城市名称
     */
    private Long regionName;

    /**
     * 地区用户数量显示
     */
    private Long userCount;

    /**
     * 地区阿姨数量
     */
    private Long caregiverCount;

    /**
     * 订单收益（商家收入总和）
     */
    private BigDecimal orderIncome;

    /**
     * 充值收益（交易类型=1且状态=1的总和）
     */
    private BigDecimal rechargeIncome;

    /**
     * 总收益 = 订单收益 + 充值收益
     */
    private BigDecimal totalIncome;

    /**
     * 阿姨收入支出
     */
    private BigDecimal orderExpense;

    /**
     * 提现支出
     */
    private BigDecimal withdrawExpense;

    /**
     * 总支出 = orderExpense + withdrawExpense
     */
    private BigDecimal totalExpense;

    /**
     * 订单盈利 = 订单收入 - 阿姨订单收入
     */
    private BigDecimal orderProfit;

    /**
     * 充值盈利 = 充值收入 - 提现支出
     */
    private BigDecimal rechargeProfit;

    /**
     *  总盈利 = 订单盈利 + 充值盈利
     */
    private BigDecimal totalProfit;

    /**
     * 总下单类型数量
     */
    private Integer serviceTypeId;

    /**
     * 对应状态下的订单总数
     */
    private Long count;

}

