package com.guanghonggu.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 订单表
 */
@Data
public class OrderDTO {
    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNumber;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 阿姨id
     */
    private Long caregiversId;

    /**
     * 地址id
     */
    private Long addressId;

    /**
     * 订单状态 (0: 等待接单，1: 已预约, 2: 进行中, 3: 已完成, 4: 取消，5退款中，6已退款，7待支付)
     */
    private Integer status;

    /**
     * 服务类型Id（1：日常保洁，2：开荒保洁，3：玻璃清洗，4：机器拆洗，5：企业保洁）
     */
    private Long serviceTypeId;

    /**
     * 预约时间
     */
    private Date appointmentTime;

    /**
     * 完成时间
     */
    private Date orderCompletionTime;

    /**
     * 服务结束时间
     */
    private Date serviceEndTime;
    /**
     * 订单金额
     */
    private BigDecimal totalPrice;

    /**
     * 优惠金额
     */
    private BigDecimal preferentialPrice;

    /**
     * 实际付款金额
     */
    private BigDecimal actualPaymentPrice;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 支付类型（1：WechatPay：微信，2：AliPay：支付宝，3：WalletPay：钱包）
     */
    private Integer payMethods;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 支付时间
     */
    private Date payTime;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 订单类型（1：立即分配，2：预约大厅，3：预约阿姨）
     */
    private Integer orderType;

    /**
     * 分页（开始的页数）
     */
    private Integer page;

    /**
     *分页（一页有多少信息）
     */
    private Integer size;

    private Boolean accept;

    /**
     * 拒绝原因
     */
    private String message;

    /**
     * 选择的取消原因ID
     */
    private Long reasonId;

    /**
     * 自定义原因（如果 reasonId 是“其他”）
     */
    private String customReason;

    /**
     * 用户是否确认了修改加价（前端需勾选确认框）
     */
    private Boolean confirmModify;

    private BigDecimal merchantIncome;

    /**
     *  对应 service_type 表中的 name 字段
     */
    private String serviceTypeName;

    private BigDecimal refundAmount;       // 实际退款金额

    private BigDecimal platformRatio;      // 平台抽成比例，比如 0.2

    // 新增字段，用于存放查询到的图片地址
    private String serviceVerificationImageUrl;

    /**
     * 接收参数
     */
    private List<ServiceVerificationImageDTO> serviceVerificationImages;  // 用于存放多个图片信息

    /**
     * service_verification_id，用于存储service_verification_id
     */
    private Long serviceVerificationId;


}