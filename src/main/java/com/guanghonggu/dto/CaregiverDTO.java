package com.guanghonggu.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 阿姨DTO
 */
@Data
public class CaregiverDTO {
    /**
     * 阿姨id
     */
    private Long caregiverId;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 微信开放id
     */
    private String wechatOpenid;

    /**
     * 状态 (0: 禁用, 1: 启用)
     */
    private Integer status;

    /**
     * 姓名
     */
    private String name;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 性别
     */
    private Integer gender;

    /**
     * 平均评分（来自评价表中三个字段的平均值，保存一位小数）
     */
    private BigDecimal avgEvaluationScore;

    /**
     * 地址id
     */
    private Long caregiverAddressId;

    /**
     * 身份证正面照片
     */
    private String idCardFront;

    /**
     * 身份证反面照片
     */
    private String idCardBack;

    /**
     * 健康证照片
     */
    private String healthCertificate;

    /**
     * 学习/考试状态（0：未学习，1：已学习，2：未考试，3：已考试）
     */
    private Integer examStatus;

    /**
     * 阿姨评分
     */
    private BigDecimal score;

    /**
     * 身份证审核（1：通过，0：未通过）
     */
    private Integer idCardVerification;

    /**
     * 健康证审核（1：通过，0：未通过）
     */
    private Integer healthCertificateVerification;

    /**
     * 审核备注/拒绝理由
     */
    private String auditRemark;

    /**
     * 注册时间
     */
    private Date registrationTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 分页（第几页开始）
     */
    private Integer page;

    /**
     * 分页（一页有多少数据）
     */
    private Integer size;
}