package com.guanghonggu.dto;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class UserVipDTO {
    /**
     * 主键ids
     */
    private Long id;

    /**
     * vip等级
     */
    private String vipLevel;

    /**
     * 储值金额
     */
    private BigDecimal rechargeBalance;

    /**
     * 优惠券id
     */
    private Long couponId;

    /**
     * 发放数量
     */
    private Integer issuedCount;


    private String couponTitle;

    private String couponDescription;

}