package com.guanghonggu.dto;

import lombok.Data;

import java.util.Date;

@Data
public class UserDTO {
    /**
     * 用户id
     */
    private Long userId;

    /**
     * 手机号
     */
    private String phoneNumber;

    /**
     * 微信开放id
     */
    private String wechatOpenid;

    /**
     * 昵称
     */
    private String nickname;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 账户状态 (0: 禁用, 1: 启用)
     */
    private Integer status;

    /**
     * 注册时间
     */
    private Date registrationTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 支付密码
     */
    private String payPassword;

    /**
     * 旧密码
     */
    private String oldPassword;

    /**
     * 新密码
     */
    private String newPassword;

    /**
     * 分页（第几页开始）
     */
    private Integer page;

    /**
     * 分页（一页有多少数据）
     */
    private Integer size;
}