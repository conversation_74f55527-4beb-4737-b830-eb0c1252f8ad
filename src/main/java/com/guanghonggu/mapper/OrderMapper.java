package com.guanghonggu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.OrderDTO;
import com.guanghonggu.dto.ServiceVerificationImageDTO;
import com.guanghonggu.dto.StatisticsDTO;
import com.guanghonggu.entity.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface OrderMapper extends BaseMapper<Order> {

    Page<Order> getAllOrderWithCaregiverName(Page<Order> orderPage, @Param("page") Integer page, @Param("size") Integer size);

    List<Order> getOrderWithCaregiverAndUser(OrderDTO orderDTO);
    /**
     * 收入
     */
    @Select("SELECT IFNULL(SUM(actual_payment_price),0) FROM `wallet_transaction`")
    BigDecimal sumOrderIncome();

    @Select("SELECT IFNULL(SUM(actual_payment_price),0) FROM `order` WHERE YEAR(create_time) = #{year}")
    BigDecimal sumOrderIncomeByYear(@Param("year") int year);

    @Select("SELECT IFNULL(SUM(actual_payment_price),0) FROM `order` WHERE YEAR(create_time) = #{year} AND MONTH(create_time) = #{month}")
    BigDecimal sumOrderIncomeByMonth(@Param("year") int year, @Param("month") int month);

    @Select("SELECT IFNULL(SUM(actual_payment_price),0) FROM `order` WHERE YEAR(create_time) = #{year} AND WEEK(create_time,1) = #{week}")
    BigDecimal sumOrderIncomeByWeek(@Param("year") int year, @Param("week") int week);

    @Select("SELECT IFNULL(SUM(actual_payment_price),0) FROM `order` WHERE DATE(create_time) = #{date}")
    BigDecimal sumOrderIncomeByDay(@Param("date") String date);

    /**
     * 支出
     */
    @Select("SELECT IFNULL(SUM(caregiver_income),0) FROM `order`")
    BigDecimal sumOrderExpense();

    @Select("SELECT IFNULL(SUM(caregiver_income),0) FROM `order` WHERE YEAR(create_time)=#{year}")
    BigDecimal sumOrderExpenseByYear(@Param("year") int year);

    @Select("SELECT IFNULL(SUM(caregiver_income),0) FROM `order` WHERE YEAR(create_time)=#{year} AND MONTH(create_time)=#{month}")
    BigDecimal sumOrderExpenseByMonth(@Param("year") int year,@Param("month") int month);

    @Select("SELECT IFNULL(SUM(caregiver_income),0) FROM `order` WHERE YEAR(create_time)=#{year} AND WEEK(create_time,1)=#{week}")
    BigDecimal sumOrderExpenseByWeek(@Param("year") int year,@Param("week") int week);

    @Select("SELECT IFNULL(SUM(caregiver_income),0) FROM `order` WHERE DATE(create_time)=#{date}")
    BigDecimal sumOrderExpenseByDay(@Param("date") String date);

    // OrderMapper.java 中需要添加的方法

    /**
     * 汇总所有订单的商户收入
     */
    BigDecimal sumMerchantIncome();

    /**
     * 按年汇总商户收入
     */
    BigDecimal sumMerchantIncomeByYear(@Param("year") int year);

    /**
     * 按月汇总商户收入
     */
    BigDecimal sumMerchantIncomeByMonth(@Param("year") int year, @Param("month") int month);

    /**
     * 按周汇总商户收入
     */
    BigDecimal sumMerchantIncomeByWeek(@Param("year") int year, @Param("week") int week);

    /**
     * 按日汇总商户收入
     */
    BigDecimal sumMerchantIncomeByDay(@Param("date") String date);

    /**
     * 按服务类型分组统计每种服务类型的订单数量
     */
    @Select("SELECT service_type_id AS serviceTypeId, COUNT(*) AS count FROM `order` GROUP BY service_type_id")
    List<StatisticsDTO> countByServiceType();

    /**
     * 地区下单类型数量统计接口
     */
    List<StatisticsDTO> countOrderTypeByRegionWithCondition(@Param("area") String area,
                                                            @Param("serviceTypeId") Long serviceTypeId);

    // 根据订单ID查询订单
    List<OrderDTO> getOrderById(@Param("orderId") Long orderId);

    // 根据service_verification_id查询所有图片
    List<ServiceVerificationImageDTO> getServiceVerificationImagesByOrderId(@Param("serviceVerificationId") Long serviceVerificationId);
}


