package com.guanghonggu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.guanghonggu.entity.WalletTransaction;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

@Mapper
public interface WalletTransactionMapper extends BaseMapper<WalletTransaction> {
    /**
     * 充值收入 - transaction_type=1且status=1的amount字段总和
     */
    @Select("SELECT IFNULL(SUM(amount),0) FROM wallet_transaction WHERE transaction_type = 1 AND status = 1")
    BigDecimal sumRechargeIncome();

    @Select("SELECT IFNULL(SUM(amount),0) FROM wallet_transaction WHERE transaction_type = 1 AND status = 1 AND YEAR(transaction_time) = #{year}")
    BigDecimal sumRechargeIncomeByYear(@Param("year") int year);

    @Select("SELECT IFNULL(SUM(amount),0) FROM wallet_transaction WHERE transaction_type = 1 AND status = 1 AND YEAR(transaction_time) = #{year} AND MONTH(transaction_time) = #{month}")
    BigDecimal sumRechargeIncomeByMonth(@Param("year") int year, @Param("month") int month);

    @Select("SELECT IFNULL(SUM(amount),0) FROM wallet_transaction WHERE transaction_type = 1 AND status = 1 AND YEAR(transaction_time) = #{year} AND WEEK(transaction_time,1) = #{week}")
    BigDecimal sumRechargeIncomeByWeek(@Param("year") int year, @Param("week") int week);

    @Select("SELECT IFNULL(SUM(amount),0) FROM wallet_transaction WHERE transaction_type = 1 AND status = 1 AND DATE(transaction_time) = #{date}")
    BigDecimal sumRechargeIncomeByDay(@Param("date") String date);

    /**
     * External Amount收入 - 之前的逻辑保留
     */
    @Select("SELECT IFNULL(SUM(external_amount),0) FROM wallet_transaction " +
            "WHERE payment_channel = 1 AND transaction_type IN (1,2) AND status = 1")
    BigDecimal sumExternalAmountTotal();

    @Select("SELECT IFNULL(SUM(external_amount),0) FROM wallet_transaction " +
            "WHERE payment_channel = 1 AND transaction_type IN (1,2) AND status = 1 " +
            "AND YEAR(transaction_time) = #{year}")
    BigDecimal sumExternalAmountByYear(@Param("year") int year);

    @Select("SELECT IFNULL(SUM(external_amount),0) FROM wallet_transaction " +
            "WHERE payment_channel = 1 AND transaction_type IN (1,2) AND status = 1 " +
            "AND YEAR(transaction_time) = #{year} AND MONTH(transaction_time) = #{month}")
    BigDecimal sumExternalAmountByMonth(@Param("year") int year, @Param("month") int month);

    @Select("SELECT IFNULL(SUM(external_amount),0) FROM wallet_transaction " +
            "WHERE payment_channel = 1 AND transaction_type IN (1,2) AND status = 1 " +
            "AND YEAR(transaction_time) = #{year} AND WEEK(transaction_time,1) = #{week}")
    BigDecimal sumExternalAmountByWeek(@Param("year") int year, @Param("week") int week);

    @Select("SELECT IFNULL(SUM(external_amount),0) FROM wallet_transaction " +
            "WHERE payment_channel = 1 AND transaction_type IN (1,2) AND status = 1 " +
            "AND DATE(transaction_time) = #{date}")
    BigDecimal sumExternalAmountByDay(@Param("date") String date);


    /**
     * 支出
     */
    @Select("SELECT IFNULL(SUM(caregiver_income),0) + IFNULL((SELECT SUM(actual_payment_price) FROM `order` WHERE status = 6),0) FROM `order`")
    BigDecimal sumWithdrawExpense();

    @Select("SELECT IFNULL(SUM(caregiver_income),0) + IFNULL((SELECT SUM(actual_payment_price) FROM `order` WHERE status = 6 AND YEAR(create_time)=#{year}),0) FROM `order` WHERE YEAR(create_time)=#{year}")
    BigDecimal sumWithdrawExpenseByYear(@Param("year") int year);

    @Select("SELECT IFNULL(SUM(caregiver_income),0) + IFNULL((SELECT SUM(actual_payment_price) FROM `order` WHERE status = 6 AND YEAR(create_time)=#{year} AND MONTH(create_time)=#{month}),0) FROM `order` WHERE YEAR(create_time)=#{year} AND MONTH(create_time)=#{month}")
    BigDecimal sumWithdrawExpenseByMonth(@Param("year") int year,@Param("month") int month);

    @Select("SELECT IFNULL(SUM(caregiver_income),0) + IFNULL((SELECT SUM(actual_payment_price) FROM `order` WHERE status = 6 AND YEAR(create_time)=#{year} AND WEEK(create_time,1)=#{week}),0) FROM `order` WHERE YEAR(create_time)=#{year} AND WEEK(create_time,1)=#{week}")
    BigDecimal sumWithdrawExpenseByWeek(@Param("year") int year,@Param("week") int week);

    @Select("SELECT IFNULL(SUM(caregiver_income),0) + IFNULL((SELECT SUM(actual_payment_price) FROM `order` WHERE status = 6 AND DATE(create_time)=#{date}),0) FROM `order` WHERE DATE(create_time)=#{date}")
    BigDecimal sumWithdrawExpenseByDay(@Param("date") String date);

}
