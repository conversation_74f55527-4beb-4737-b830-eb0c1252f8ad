package com.guanghonggu.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.entity.Complaint;
import com.guanghonggu.entity.Order;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
* <AUTHOR>
* @description 针对表【complaint(投诉表)】的数据库操作Mapper
* @createDate 2025-04-27 10:56:56
* @Entity com.guanghonggu.entity.Complaint
*/
@Mapper
public interface ComplaintMapper extends BaseMapper<Complaint> {
    Page<Complaint> getAllComplaintWithCaregiverName(Page<Order> orderPage, @Param("page") Integer page, @Param("size") Integer size);

}




