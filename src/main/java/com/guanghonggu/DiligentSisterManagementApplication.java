package com.guanghonggu;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@SpringBootApplication
@MapperScan("com.guanghonggu.mapper")  // 指定扫描 Mapper 接口的包路径
public class DiligentSisterManagementApplication {

    public static void main(String[] args) {
        SpringApplication.run(DiligentSisterManagementApplication.class, args);
    }


}

