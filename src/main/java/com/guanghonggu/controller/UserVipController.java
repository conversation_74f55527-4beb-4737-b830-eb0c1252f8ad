package com.guanghonggu.controller;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserVipDTO;
import com.guanghonggu.entity.UserVip;
import com.guanghonggu.service.UserVipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/userVip")
public class UserVipController {
    @Autowired
    private UserVipService userVipService;

    @GetMapping("/getUserVip")
    public ResultDTO<List<UserVip>> getUserVip() {
        try{
            List<UserVip> userVip = userVipService.getUserVip();
            return ResultDTO.success("查询成功",userVip);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    @GetMapping("/getUserVipByVipId")
    public ResultDTO<UserVip> getUserVipByVipId(@RequestBody UserVipDTO userVipDTO) {
        try{
            UserVip userVip = userVipService.getUserVipByVipId(userVipDTO);
            return ResultDTO.success("查询成功",userVip);
        }catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    @PostMapping("/addUserVip")
    public ResultDTO<Integer> addUserVip(@RequestBody UserVipDTO userVipDTO) {
        try{
            int addUserVip = userVipService.addUserVip(userVipDTO);
            return ResultDTO.success("添加成功",addUserVip);
        }catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("添加失败" + e.getMessage());
        }
    }

    @PutMapping("/updateUserVip")
    public ResultDTO<Integer> updateUserVip(@RequestBody UserVipDTO userVipDTO) {
        try{
            int updateUserVip = userVipService.updateUserVip(userVipDTO);
            return ResultDTO.success("更新成功",updateUserVip);
        }catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("更新失败" + e.getMessage());
        }
    }

    @DeleteMapping("/deleteUserVip")
    public ResultDTO<Integer> deleteUserVip(@RequestBody UserVipDTO userVipDTO) {
        try{
            int deleteUserVip = userVipService.deleteUserVip(userVipDTO);
            return ResultDTO.success("删除成功",deleteUserVip);
        }catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("删除失败" + e.getMessage());
        }
    }
}
