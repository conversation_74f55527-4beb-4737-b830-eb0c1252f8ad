package com.guanghonggu.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.service.ComplaintService;
import com.guanghonggu.dto.ComplaintDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Complaint;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/complaint")
public class ComplaintController {
    @Autowired
    private ComplaintService complaintService;

    @GetMapping("/getComplaint")
    public ResultDTO<Page<Complaint>> getComplaint(ComplaintDTO complaintDTO) {
        try{
            if (complaintDTO == null) {
                complaintDTO = new ComplaintDTO();
            }
            Integer page = complaintDTO.getPage() != null ? complaintDTO.getPage() : 1;
            Integer size = complaintDTO.getSize() != null ? complaintDTO.getSize() : 8;

            Page<Complaint> complaint = complaintService.getComplaint(
                    page,size
            );
            return ResultDTO.success("查询成功", complaint);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

}
