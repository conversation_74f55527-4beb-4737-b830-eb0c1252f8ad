package com.guanghonggu.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.service.CouponService;
import com.guanghonggu.dto.CouponCreateDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Coupon;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/coupon")
public class CouponController {

    @Autowired
    private CouponService couponService;

    /**
     * 查询优惠券
     */
    @PostMapping("/getCoupon")
    public ResultDTO<Page<Coupon>> getCoupon(@RequestBody(required = false) CouponCreateDTO couponCreateDTO) {
        try{
            if (couponCreateDTO == null) {
                couponCreateDTO = new CouponCreateDTO();
            }
            Integer page = couponCreateDTO.getPage() != null ? couponCreateDTO.getPage() : 1;
            Integer size = couponCreateDTO.getSize() != null ? couponCreateDTO.getSize() : 10;
            Page<Coupon> getCoupon = couponService.getAllCoupon(page,size,couponCreateDTO.getIsDelete());
            return ResultDTO.success("查询成功",getCoupon);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    /**
     * 创建优惠券
     */
    @PostMapping("/addCoupon")
    public ResultDTO<String> addCoupon(@RequestBody CouponCreateDTO couponCreateDTO) {
        try{
           return couponService.postCoupon(couponCreateDTO);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("添加失败" + e.getMessage());
        }
    }

    /**
     * 编辑优惠券
     */
    @PutMapping("/putCoupon")
    public ResultDTO<Integer> putCoupon(@RequestBody CouponCreateDTO couponCreateDTO) {
        try{
            Long couponId = couponCreateDTO.getCouponId();
            couponCreateDTO.setCouponId(couponId);
            int putCoupon = couponService.updateCoupon(couponCreateDTO);
            if (putCoupon > 0) {
                return ResultDTO.success("更新成功", putCoupon);
            }else {
                return ResultDTO.error("未找到id");
            }
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("更新失败" + e.getMessage());
        }
    }

    /**
     * 删除优惠券
     */
    @PutMapping("/deleteCoupon")
    public ResultDTO<Integer> deleteCoupon(@RequestBody CouponCreateDTO couponCreateDTO) {
        try{
            int delete = couponService.deleteCoupon(couponCreateDTO);
            return ResultDTO.success("删除成功",delete);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("删除失败" + e.getMessage());
        }
    }

}
