package com.guanghonggu.controller;

import com.guanghonggu.dto.GiftCouponProbabilityDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.GiftCouponProbability;
import com.guanghonggu.service.GiftCouponProbabilityService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/giftCouponProbability")
public class GiftCouponProbabilityController {
    @Autowired
    private GiftCouponProbabilityService giftCouponProbabilityService;

    @GetMapping("/getGiftCouponProbability")
    public ResultDTO<List<GiftCouponProbability>> getGiftCouponProbability() {
        try {
            List<GiftCouponProbability> giftCouponProbability = giftCouponProbabilityService.getGiftCouponProbability();
            return ResultDTO.success("查询成功", giftCouponProbability);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    @GetMapping("/getGiftCouponProbabilityById")
    public ResultDTO<GiftCouponProbability> getGiftCouponProbabilityById(@RequestBody GiftCouponProbabilityDTO giftCouponProbabilityDTO) {
        try {
            GiftCouponProbability giftCouponProbability = giftCouponProbabilityService.getGiftCouponProbabilityById(giftCouponProbabilityDTO);
            return ResultDTO.success("查询成功", giftCouponProbability);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    @PostMapping("/addGiftCouponProbability")
    public ResultDTO<Integer> addGiftCouponProbability(@RequestBody GiftCouponProbabilityDTO giftCouponProbabilityDTO) {
        try {
            int addGiftCouponProbability = giftCouponProbabilityService.addGiftCouponProbability(giftCouponProbabilityDTO);
            return ResultDTO.success("添加成功", addGiftCouponProbability);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("添加失败" + e.getMessage());
        }
    }

    @PutMapping("/putGiftCouponProbability")
    public ResultDTO<Integer> putGiftCouponProbability(@RequestBody GiftCouponProbabilityDTO giftCouponProbabilityDTO) {
        try {
            int putGiftCouponProbability = giftCouponProbabilityService.updateGiftCouponProbability(giftCouponProbabilityDTO);
            return ResultDTO.success("更新成功", putGiftCouponProbability);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("更新失败" + e.getMessage());
        }
    }

    @DeleteMapping("/deleteGiftCouponProbability")
    public ResultDTO<Integer> deleteGiftCouponProbability(@RequestBody GiftCouponProbabilityDTO giftCouponProbabilityDTO) {
        try {
            int delete = giftCouponProbabilityService.deleteGiftCouponProbability(giftCouponProbabilityDTO);
            return ResultDTO.success("删除成功", delete);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("删除失败" + e.getMessage());
        }
    }
}