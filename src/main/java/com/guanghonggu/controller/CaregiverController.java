package com.guanghonggu.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.service.CaregiverService;
import com.guanghonggu.dto.CaregiverDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.Caregiver;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/caregiver")
public class CaregiverController {
    @Autowired
    private CaregiverService caregiverService;

    /**
     * 查询全部阿姨信息
     */
    @PostMapping("/getCaregiver")
    public ResultDTO<Page<Caregiver>> getCaregiver(@RequestBody(required = false) CaregiverDTO caregiverDTO) {
        try{
            if (caregiverDTO == null) {
                caregiverDTO = new CaregiverDTO();
            }
            Integer page = caregiverDTO.getPage() != null ? caregiverDTO.getPage() : 1;
            Integer size = caregiverDTO.getSize() != null ? caregiverDTO.getSize() : 10;
            Page<Caregiver> getCaregiver = caregiverService.getAllCaregiver(page,size);
            return ResultDTO.success("查询阿姨信息成功",getCaregiver);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询阿姨信息失败" + e.getMessage());
        }
    }

    /**
     * 根据阿姨id查询
     */
    @PostMapping("/getCaregiverId")
    public ResultDTO<List<Caregiver>> getCaregiverId(@RequestBody CaregiverDTO caregiverDTO) {
        try{
            List<Caregiver> getCaregiver = caregiverService.getCaregiverId(caregiverDTO);
            return ResultDTO.success("查询成功",getCaregiver);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }


    /**
     * 查询阿姨总数量
     */
    @GetMapping(value = "total")
    public ResultDTO<Long> getTotal() {
        try{
            long count = caregiverService.count();
            return ResultDTO.success("查询成功",count);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败"+e.getMessage());
        }
    }

    /**
     * 获取审核图片
     */
    @PostMapping("/audit-images")
    public ResultDTO<CaregiverDTO> getAuditImages(@RequestBody CaregiverDTO param) {
        try {
            Long caregiverId = param.getCaregiverId(); // 从 JSON 中提取 ID
            CaregiverDTO caregiverDTO = caregiverService.getAuditImages(caregiverId);
            return ResultDTO.success("获取审核图片成功", caregiverDTO);
        } catch (Exception e) {
            return ResultDTO.error("获取审核图片失败：" + e.getMessage());
        }
    }

    /**
     * 审核图片是否通过
     */
    @PutMapping("/putCaregiver")
    public ResultDTO<Integer> putCaregiver(@RequestBody CaregiverDTO caregiverDTO) {
        try{
            Long caregiverId = caregiverDTO.getCaregiverId();
            caregiverDTO.setCaregiverId(caregiverId);
            int result = caregiverService.updateCaregiver(caregiverDTO);
            return ResultDTO.success("更新成功",result);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("更改失败" + e.getMessage());
        }
    }
}
