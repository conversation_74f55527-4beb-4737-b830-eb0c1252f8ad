package com.guanghonggu.controller;

import com.guanghonggu.service.AdminUserService;
import com.guanghonggu.dto.AdminUserDTO;
import com.guanghonggu.dto.ResultDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@RestController
@RequestMapping("/adminUser")
public class AdminUserController {

    @Autowired
    private AdminUserService adminUserService;

    @PostMapping("/login")
    public ResultDTO<Map<String, Object>> login(@RequestBody AdminUserDTO adminUserDTO) {
        try {
            Map<String, Object> loginResult = adminUserService.login(adminUserDTO);
            return ResultDTO.success("登录成功", loginResult);
        } catch (RuntimeException e) {
            return ResultDTO.error("登录失败" + e.getMessage());
        }
    }

    @PostMapping("/addAccount")
    public ResultDTO<String> addAccount(@RequestBody AdminUserDTO adminUserDTO) {
        try{
            String token = adminUserService.addAccount(adminUserDTO);
            return ResultDTO.success("注册成功", token);
        }catch (RuntimeException e){
            return ResultDTO.error("注册失败" + e.getMessage());
        }
    }
}
