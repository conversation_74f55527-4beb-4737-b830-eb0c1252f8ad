package com.guanghonggu.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.service.OrderService;
import com.guanghonggu.vo.OrderWithRejectReasonVO;
import com.guanghonggu.dto.IncomeQueryDTO;
import com.guanghonggu.dto.OrderDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.StatisticsDTO;
import com.guanghonggu.entity.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/order")
public class OrderController {

    @Autowired
    private OrderService orderService;

    /**
     * 查询全部订单
     */
    @PostMapping("/getOrder")
    public ResultDTO<Page<Order>> getOrder(@RequestBody(required = false) OrderDTO orderDTO) {
        try{
            Integer page = orderDTO.getPage() != null ? orderDTO.getPage() : 1;
            Integer size = orderDTO.getSize() != null ? orderDTO.getSize() : 10;

            Page<Order> getOrder = orderService.getAllOrder(page,size);
            return ResultDTO.success("查询成功",getOrder);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    /**
     * 根据订单id查询订单信息
     */
    @PostMapping("/orderId")
    public ResultDTO<List<OrderDTO>> getOrderById(@RequestBody(required = false) OrderDTO orderDTO) {
        try{
            List<OrderDTO> orderById = orderService.getOrderById(orderDTO.getOrderId());
            return ResultDTO.success("根据订单id查询成功",orderById);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("根据订单id查询失败" + e.getMessage());
        }
    }

    /**
     * 根据订单编号查询订单
     */
    @PostMapping("/getOrderId")
    public ResultDTO<List<Order>> getOrderId(@RequestBody(required = false) OrderDTO orderDTO) {
        try{
            List<Order> orderList = orderService.getOrderId(orderDTO);
            return ResultDTO.success("查询成功",orderList);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    /**
     * 查询订单收益、充值收益与总收益
     * @return 在 DTO 中以三个字段返回
     */
    @PostMapping("/statistics/income/all")
    public ResultDTO<StatisticsDTO> allStats() {
        return ResultDTO.success("查询全部收入成功", orderService.getIncomeStatistics());
    }

    @PostMapping("/statistics/income/year")
    public ResultDTO<StatisticsDTO> statsByYear(@RequestBody IncomeQueryDTO req) {
        return ResultDTO.success("按年查询成功", orderService.getIncomeByYear(req.getYear()));
    }

    @PostMapping("/statistics/income/month")
    public ResultDTO<StatisticsDTO> statsByMonth(@RequestBody IncomeQueryDTO req) {
        return ResultDTO.success("按月查询成功", orderService.getIncomeByMonth(req.getYear(), req.getMonth()));
    }

    @PostMapping("/statistics/income/week")
    public ResultDTO<StatisticsDTO> statsByWeek(@RequestBody IncomeQueryDTO req) {
        return ResultDTO.success("按周查询成功", orderService.getIncomeByWeek(req.getYear(), req.getWeek()));
    }

    @PostMapping("/statistics/income/day")
    public ResultDTO<StatisticsDTO> statsByDay(@RequestBody IncomeQueryDTO req) {
        return ResultDTO.success("按日查询成功", orderService.getIncomeByDay(req.getDate()));
    }

    /**
     * 支出
     */
    @PostMapping("/statistics/expense/all")
    public ResultDTO<StatisticsDTO> allExpense() {
        return ResultDTO.success("查询全部支出成功", orderService.queryAll());
    }

    @PostMapping("/statistics/expense/year")
    public ResultDTO<StatisticsDTO> expenseByYear(@RequestBody IncomeQueryDTO req) {
        return ResultDTO.success("按年查询成功", orderService.queryByYear(req.getYear()));
    }

    @PostMapping("/statistics/expense/month")
    public ResultDTO<StatisticsDTO> expenseByMonth(@RequestBody IncomeQueryDTO req) {
        return ResultDTO.success("按月查询成功", orderService.queryByMonth(req.getYear(), req.getMonth()));
    }

    @PostMapping("/statistics/expense/week")
    public ResultDTO<StatisticsDTO> expenseByWeek(@RequestBody IncomeQueryDTO req) {
        return ResultDTO.success("按周查询成功", orderService.queryByWeek(req.getYear(), req.getWeek()));
    }

    @PostMapping("/statistics/expense/day")
    public ResultDTO<StatisticsDTO> expenseByDay(@RequestBody IncomeQueryDTO req) {
        return ResultDTO.success("按日查询成功", orderService.queryByDay(req.getDate()));
    }

    /**
     * 收益
     */
    @PostMapping("/statistics/profit/all")
    public ResultDTO<StatisticsDTO> allProfit() {
        return ResultDTO.success("查询全部收益成功", orderService.queryProfitAll());
    }

    @PostMapping("/statistics/profit/year")
    public ResultDTO<StatisticsDTO> profitByYear(@RequestBody IncomeQueryDTO req) {
        return ResultDTO.success("按年查询成功", orderService.queryProfitByYear(req.getYear()));
    }

    @PostMapping("/statistics/profit/month")
    public ResultDTO<StatisticsDTO> profitByMonth(@RequestBody IncomeQueryDTO req) {
        return ResultDTO.success("按月查询成功", orderService.queryProfitByMonth(req.getYear(), req.getMonth()));
    }

    @PostMapping("/statistics/profit/week")
    public ResultDTO<StatisticsDTO> profitByWeek(@RequestBody IncomeQueryDTO req) {
        return ResultDTO.success("按周查询成功", orderService.queryProfitByWeek(req.getYear(), req.getWeek()));
    }

    @PostMapping("/statistics/profit/day")
    public ResultDTO<StatisticsDTO> profitByDay(@RequestBody IncomeQueryDTO req) {
        return ResultDTO.success("按日查询成功", orderService.queryProfitByDay(req.getDate()));
    }

    /**
     * 获取所有服务类型及其对应的订单数量
     */
    @GetMapping("/type/counts")
    public ResultDTO<List<StatisticsDTO>> getOrderTypeCounts() {
        List<StatisticsDTO> list = orderService.getOrderTypeCounts();
        return ResultDTO.success("查询成功", list);
    }

    /**
     * 地区下单类型数量统计接口
     */
    @PostMapping("/region-order-type-count")
    public ResultDTO<List<StatisticsDTO>> getRegionOrderTypeCount(@RequestBody Map<String, Object> params) {
        List<StatisticsDTO> result = orderService.countOrderTypeByRegionWithCondition(params);
        return ResultDTO.success("查询成功", result);
    }

    /**
     * 查询退款审批
     */
    @PostMapping("/getStatus")
    public ResultDTO<Page<OrderWithRejectReasonVO>> getStatus(@RequestBody(required = false) OrderDTO orderDTO){
        try{
            if (orderDTO == null){
                orderDTO = new OrderDTO();
            }
            Integer page = orderDTO.getPage() != null ? orderDTO.getPage() : 1;
            Integer size = orderDTO.getSize() != null ? orderDTO.getSize() : 10;
            Integer status = orderDTO.getStatus() !=null ? orderDTO.getStatus() : 5;

            Page<OrderWithRejectReasonVO> getStatus = orderService.getOrderStatus(page,size,orderDTO,status);
            return ResultDTO.success("查询成功",  getStatus);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    /**
     * 退款审批
     */
    @PutMapping("/putStatus")
    public ResultDTO<Integer> putStatus(@RequestBody OrderDTO orderDTO){
        try{
            Long orderId = orderDTO.getOrderId();
            orderDTO.setOrderId(orderId);
            int putStatus = orderService.updateStatus(orderDTO);
            return ResultDTO.success("更新成功", putStatus);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("更新失败" + e.getMessage());
        }
    }

}
