package com.guanghonggu.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.service.SysConfigService;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.SysConfigDTO;
import com.guanghonggu.entity.SysConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/sysConfig")
public class SysConfigController {
    @Autowired
    private SysConfigService sysConfigService;

    @PostMapping("/postSysConfig")
    public ResultDTO<Integer> addSysConfig(@RequestBody SysConfigDTO sysConfigDTO) {
        try{
            int postSysConfig = sysConfigService.addSysConfig(sysConfigDTO);
            return ResultDTO.success("新增成功",postSysConfig);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("新增失败" + e.getMessage());
        }
    }

    @PutMapping("/putSysConfig")
    public ResultDTO<Integer> updateSysConfig(@RequestBody SysConfigDTO sysConfigDTO) {
        try{
            Long id = sysConfigDTO.getId();
            sysConfigDTO.setId(id);
            int result = sysConfigService.updateSysConfig(sysConfigDTO);
            if(id > 0){
                return ResultDTO.success("修改成功",result);
            }else {
                return ResultDTO.error("更新失败，未找到地址");
            }
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("修改失败" + e.getMessage());
        }
    }

    @PostMapping("/getId")
    public ResultDTO<List<SysConfig>> getSysConfig(@RequestBody SysConfigDTO sysConfigDTO) {
        try{
            List<SysConfig> getSysConfig = sysConfigService.getIdSysConfig(sysConfigDTO);
            return ResultDTO.success("根据id查询成功",getSysConfig);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("根据id查询失败" + e.getMessage());
        }
    }

    @PostMapping("/getSysConfig")
    public ResultDTO<Page<SysConfig>> getSysConfigPage(@RequestBody(required = false) SysConfigDTO sysConfigDTO) {
        try{
            if(sysConfigDTO == null){
                sysConfigDTO = new SysConfigDTO();
            }
            Integer page = sysConfigDTO.getPage() != null ? sysConfigDTO.getPage() : 1;
            Integer size = sysConfigDTO.getSize() != null ? sysConfigDTO.getSize() : 10;

            Page<SysConfig> getAllSysConfig = sysConfigService.getAllSysConfig(page,size);
            return ResultDTO.success("查询成功",getAllSysConfig);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    @DeleteMapping("/deleteSysConfig")
    public ResultDTO<Integer> deleteSysConfig(@RequestBody SysConfigDTO sysConfigDTO) {
        try{
            int delete = sysConfigService.deleteSysConfig(sysConfigDTO);
            return ResultDTO.success("删除成功",delete);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("删除失败" + e.getMessage());
        }
    }


}
