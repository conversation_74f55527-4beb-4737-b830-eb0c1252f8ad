package com.guanghonggu.controller;

import com.guanghonggu.service.SysPictureService;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.SysPictureDTO;
import com.guanghonggu.entity.SysPicture;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@RestController
@RequestMapping("/sysPicture")
public class SysPictureController {
    @Autowired
    private SysPictureService sysPictureService;

    /**
     * 查询首页图片
     */
    @PostMapping("/getSysPicture")
    public ResultDTO<List<SysPicture>> getSysPicture(@RequestBody(required = false) SysPictureDTO sysPictureDTO) {
        try{
            if (sysPictureDTO == null) {
                sysPictureDTO = new SysPictureDTO();
            }
            List<SysPicture> getSysPicture = sysPictureService.getAllSysPicture(sysPictureDTO);
            return ResultDTO.success("查询成功",getSysPicture);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败"+ e.getMessage());
        }
    }

    /**
     * 上传图片到阿里云 OSS，并将文件路径保存进数据库（仅保存 objectName）
     */
    @PostMapping("/upload")
    public ResultDTO<String> uploadPicture(
            @RequestParam String pictureName,
            @RequestParam Integer pictureType,
            @RequestParam(required = false) String jumpLink,
            @RequestPart("file") MultipartFile file) {
        try {
            SysPictureDTO sysPictureDTO = new SysPictureDTO();
            sysPictureDTO.setPictureName(pictureName);
            sysPictureDTO.setPictureType(pictureType);
            sysPictureDTO.setJumpLink(jumpLink);

            sysPictureService.uploadPicture(sysPictureDTO, file);
            return ResultDTO.success("图片上传成功", null);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("图片上传失败：" + e.getMessage());
        }
    }

    /**
     * 根据图片 ID 生成签名预览链接（用于私有 Bucket）
     */
    @GetMapping("/preview/{id}")
    public ResultDTO<String> previewPicture(@PathVariable Long id) {
        try {
            String previewUrl = sysPictureService.generatePreviewUrl(id);
            return ResultDTO.success("生成签名链接成功", previewUrl);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("生成签名链接失败：" + e.getMessage());
        }
    }

    /**
     * 删除首页图片
     */
    @DeleteMapping("/deleteSysPicture")
    public ResultDTO<Long> deleteSysPicture(@RequestBody SysPictureDTO sysPictureDTO) {
        try{
            long delete = sysPictureService.deleteSysPicture(sysPictureDTO);
            return ResultDTO.success("删除成功",delete);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("删除成功"+ e.getMessage());
        }
    }

    /**
     * 修改系统图片（支持更换图片文件）
     */
    @PutMapping("/update")
    public ResultDTO<String> updatePicture(
            @RequestParam(required = false) Long pictureId,
            @RequestParam(required = false) String pictureName,
            @RequestParam(required = false) Integer pictureType,
            @RequestParam(required = false) String jumpLink,
            @RequestPart(value = "file", required = false) MultipartFile file,
            HttpServletRequest request) {
        try {
            // 详细调试信息
            System.out.println("=== 调试信息 ===");
            System.out.println("pictureId 参数: " + pictureId);
            System.out.println("pictureName 参数: " + pictureName);
            System.out.println("pictureType 参数: " + pictureType);
            System.out.println("jumpLink 参数: " + jumpLink);
            System.out.println("file 参数: " + (file != null ? file.getOriginalFilename() : "null"));

            // 打印所有请求参数
            System.out.println("所有请求参数:");
            request.getParameterMap().forEach((key, values) -> {
                System.out.println(key + " = " + String.join(",", values));
            });

            // 手动构建 DTO
            SysPictureDTO sysPictureDTO = new SysPictureDTO();
            sysPictureDTO.setPictureId(pictureId);
            sysPictureDTO.setPictureName(pictureName);
            sysPictureDTO.setPictureType(pictureType);
            sysPictureDTO.setJumpLink(jumpLink);

            System.out.println("构建的 DTO: " + sysPictureDTO);

            sysPictureService.updatePicture(sysPictureDTO, file);
            return ResultDTO.success("图片修改成功", null);
        } catch (Exception e) {
            e.printStackTrace();
            return ResultDTO.error("图片修改失败: " + e.getMessage());
        }
    }
}
