package com.guanghonggu.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.service.UserService;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserDTO;
import com.guanghonggu.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/user")
public class UserController {
    @Autowired
    private UserService userService;

    /**
     * 查询全部用户
     */
    @PostMapping("/getUser")
    public ResultDTO<Page<User>> getUser(@RequestBody(required = false)UserDTO userDTO) {
        try{
            if(userDTO==null){
                userDTO=new UserDTO();
            }
            Integer page = userDTO.getPage() != null ? userDTO.getPage() : 1;
            Integer size = userDTO.getSize() != null ? userDTO.getSize() : 10;
            Page<User> getUser = userService.getAllUser(page,size);
            return ResultDTO.success("查询成功",getUser);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    /**
     * 根据用户id查询用户信息
     */
    @PostMapping("/getUserId")
    public ResultDTO<List<User>> getUserId(@RequestBody(required = false)UserDTO userDTO) {
        try{
            List<User> userList = userService.getUserId(userDTO);
            return ResultDTO.success("查询成功",userList);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    /**
     * 获取全部用户数量
     */
    @GetMapping("/total")
    public ResultDTO<Long> getTotalUserCount() {
        try {
            long count = userService.count(); // MyBatis-Plus 提供的统计方法
            return ResultDTO.success("查询成功", count);
        } catch (Exception e) {
            return ResultDTO.error("查询失败：" + e.getMessage());
        }
    }
}
