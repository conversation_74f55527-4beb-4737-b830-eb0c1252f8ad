package com.guanghonggu.controller;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserMessageDTO;
import com.guanghonggu.service.UserMessageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/userMessage")
public class UserMessageController {

    @Autowired
    private UserMessageService userMessageService;

    @PostMapping("/sendNotification")
    public ResultDTO<?> sendNotification(@RequestBody UserMessageDTO userMessageDTO) {
        try {
            // 调用 service 层逻辑，将消息下发给指定类型（用户或阿姨）
            return userMessageService.sendNotificationToRecipients(userMessageDTO.getMessageId(), userMessageDTO.getUserType());
        } catch (Exception e) {
            return ResultDTO.error("消息下发失败: " + e.getMessage());
        }
    }
}



