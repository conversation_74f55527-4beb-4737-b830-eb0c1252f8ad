package com.guanghonggu.controller;

import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.UserCouponDTO;
import com.guanghonggu.service.UserCouponService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/userCoupon")
public class UserCouponController {
    @Autowired
    private UserCouponService userCouponService;

    @PostMapping("/distributeCoupon")
    public ResultDTO distributeCoupon(@RequestBody UserCouponDTO userCouponDTO) {
        try{
            return userCouponService.distributeCoupon(userCouponDTO);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("下发失败" + e.getMessage());
        }
    }

}
