package com.guanghonggu.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.service.MessageService;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.dto.MessageDTO;
import com.guanghonggu.entity.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/message")
public class MessageController {
    @Autowired
    private MessageService messageService;

    @PostMapping("/getMessage")
    public ResultDTO<Page<Message>> getMessage(@RequestBody(required = false) MessageDTO messageDTO) {
        try{
            if(messageDTO==null){
                messageDTO=new MessageDTO();
            }
            Integer page=messageDTO.getPage() !=null?messageDTO.getPage():1;
            Integer size=messageDTO.getSize() !=null?messageDTO.getSize():10;

            Page<Message> getMessage = messageService.getAllMessage(page,size);
            return ResultDTO.success("查询成功",getMessage);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    @PostMapping("/postMessage")
    public ResultDTO<Integer> postTest(@RequestBody MessageDTO messageDTO) {
        try{
            int postMessage = messageService.addMessage(messageDTO);
            return ResultDTO.success("添加成功",postMessage);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("添加失败" + e.getMessage());
        }
    }

    @PutMapping("/putMessage")
    public ResultDTO<Integer> putMessage(@RequestBody MessageDTO messageDTO) {
        try{
            Long id = messageDTO.getId();
            messageDTO.setId(id);
            int putMessage = messageService.updateMessage(messageDTO);
            return ResultDTO.success("更新成功",putMessage);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("更新失败" + e.getMessage());
        }
    }

    @DeleteMapping("/deleteMessage")
    public ResultDTO<Integer> deleteMessage(@RequestBody MessageDTO messageDTO) {
        try{
            int delete = messageService.deleteMessage(messageDTO);
            return ResultDTO.success("删除成功",delete);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("删除失败" + e.getMessage());
        }
    }

}
