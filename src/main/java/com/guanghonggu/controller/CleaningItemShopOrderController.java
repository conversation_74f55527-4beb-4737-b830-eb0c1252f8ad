package com.guanghonggu.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.guanghonggu.dto.CleaningItemShopOrderDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CleaningItemShopOrder;
import com.guanghonggu.service.CleaningItemShopOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/cleaningItemShopOrder")
public class CleaningItemShopOrderController {
    @Autowired
    private CleaningItemShopOrderService cleaningItemShopOrderService;

    @GetMapping("/getAllCleaningItemShopOrder")
    public ResultDTO<Page<CleaningItemShopOrder>> getAllCleaningItemShopOrder(CleaningItemShopOrderDTO cleaningItemShopOrderDTO){
        try{
            if (cleaningItemShopOrderDTO == null) {
                cleaningItemShopOrderDTO = new CleaningItemShopOrderDTO();
            }
            Integer page = cleaningItemShopOrderDTO.getPage() != null ? cleaningItemShopOrderDTO.getPage() : 1;
            Integer size = cleaningItemShopOrderDTO.getSize() != null ? cleaningItemShopOrderDTO.getSize() : 10;
            Page<CleaningItemShopOrder> allCleaningItemShopOrder = cleaningItemShopOrderService.getAllCleaningItemShopOrder(page,size,cleaningItemShopOrderDTO);
            return ResultDTO.success("查询成功",allCleaningItemShopOrder);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    @PostMapping("/getCleaningItemShopOrder")
    public ResultDTO<CleaningItemShopOrder> getCleaningItemShopOrder(@RequestBody(required = false) CleaningItemShopOrderDTO cleaningItemShopOrderDTO){
        try{
            CleaningItemShopOrder cleaningItemShopOrder = cleaningItemShopOrderService.getCleaningItemShopOrder(cleaningItemShopOrderDTO);
            return ResultDTO.success("查询成功",cleaningItemShopOrder);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    @PutMapping("/modifyOrderStatus")
    public ResultDTO<Integer> modifyOrderStatus(@RequestBody CleaningItemShopOrderDTO cleaningItemShopOrderDTO){
        try{
            Integer status = cleaningItemShopOrderService.modifyOrderStatus(cleaningItemShopOrderDTO);
            return ResultDTO.success("修改成功",status);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("修改失败" + e.getMessage());
        }
    }
}
