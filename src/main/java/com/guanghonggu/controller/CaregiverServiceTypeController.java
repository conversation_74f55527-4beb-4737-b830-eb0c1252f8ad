package com.guanghonggu.controller;

import com.guanghonggu.dto.CaregiverServiceTypeDTO;
import com.guanghonggu.dto.ResultDTO;
import com.guanghonggu.entity.CaregiverServiceType;
import com.guanghonggu.service.CaregiverServiceTypeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/caregiverServiceType")
public class CaregiverServiceTypeController {

    @Autowired
    private CaregiverServiceTypeService caregiverServiceTypeService;

    @GetMapping("/queryPermission")
    public ResultDTO<List<CaregiverServiceType>> queryPermissionCaregiverServiceType(CaregiverServiceTypeDTO caregiverServiceTypeDTO) {
        try{
            List<CaregiverServiceType> queryPermission = caregiverServiceTypeService.queryPermissionCaregiverServiceType(caregiverServiceTypeDTO);
            return ResultDTO.success("查询成功", queryPermission);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("查询失败" + e.getMessage());
        }
    }

    @PostMapping("/addPermission")
    public ResultDTO<?> addPermissionCaregiverServiceType(@RequestBody CaregiverServiceTypeDTO caregiverServiceTypeDTO) {
        try{
           List<CaregiverServiceType> caregiverServiceTypeList = caregiverServiceTypeDTO.getCaregiverServiceTypeList();
            caregiverServiceTypeService.addCaregiverServiceTypes(caregiverServiceTypeList);
            return ResultDTO.success("添加权限成功", null);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("添加失败" + e.getMessage());
        }
    }

    @DeleteMapping("/deletePermission")
    public ResultDTO<Long> deletePermissionCaregiverServiceType(@RequestBody CaregiverServiceTypeDTO caregiverServiceTypeDTO) {
        try{
            Long deletePermission = caregiverServiceTypeService.deletePermissionCaregiverServiceType(caregiverServiceTypeDTO);
            return ResultDTO.success("删除成功", deletePermission);
        }catch (Exception e){
            e.printStackTrace();
            return ResultDTO.error("删除成功" + e.getMessage());
        }
    }

}
