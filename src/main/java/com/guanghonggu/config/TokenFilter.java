package com.guanghonggu.config;

import cn.dev33.satoken.stp.StpUtil;
import cn.dev33.satoken.exception.NotLoginException;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@Component
public class TokenFilter extends OncePerRequestFilter {

    // 白名单路径，这些路径不需要token验证
    private static final List<String> WHITE_LIST = Arrays.asList(
            "/login",
            "/adminUser/login",
            "/register",
            "/public",
            "/health",
            "/swagger-ui",
            "/v3/api-docs"
    );

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        String requestPath = request.getRequestURI();

        // 检查是否在白名单中
        if (isWhiteListPath(requestPath)) {
            filterChain.doFilter(request, response);
            return;
        }

        // 获取token头中的token
        String authHeader = request.getHeader("token");
        String token = null;

        // 检查token格式 - 支持两种格式
        if (authHeader != null) {
            if (authHeader.startsWith("Bearer ")) {
                token = authHeader.substring(7); // 去掉 "Bearer " 前缀
            } else {
                // 直接使用token值（兼容前端直接传递token的情况）
                token = authHeader;
            }
        }

        // 如果没有token或token为空
        if (token == null || token.trim().isEmpty()) {
            sendErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED, "Token is missing");
            return;
        }

        try {
            // 先尝试 Sa-Token 验证
            Object loginId = StpUtil.getLoginIdByToken(token);
            if (loginId == null) {
                throw new NotLoginException("Token无效", StpUtil.getLoginType(), NotLoginException.INVALID_TOKEN);
            }

            // 设置当前登录的用户ID到Sa-Token的上下文中
            StpUtil.getLoginId(loginId);

            // 可选：将用户信息设置到request中
            request.setAttribute("userId", loginId);

        } catch (NotLoginException e) {
            // Sa-Token抛出的登录异常
            String message = getErrorMessage(e.getType());
            sendErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED, message);
            return;
        } catch (Exception e) {
            // 其他异常
            sendErrorResponse(response, HttpServletResponse.SC_UNAUTHORIZED, "Token验证失败");
            return;
        }

        // token验证通过，继续执行后续的过滤器和控制器
        filterChain.doFilter(request, response);
    }

    /**
     * 检查路径是否在白名单中
     */
    private boolean isWhiteListPath(String path) {
        return WHITE_LIST.stream().anyMatch(whitePath -> path.startsWith(whitePath));
    }

    /**
     * 发送错误响应
     */
    private void sendErrorResponse(HttpServletResponse response, int status, String message) throws IOException {
        response.setStatus(status);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write("{\"code\":" + status + ",\"message\":\"" + message + "\"}");
    }

    /**
     * 根据Sa-Token异常类型获取错误信息
     */
    private String getErrorMessage(String exceptionType) {
        switch (exceptionType) {
            case NotLoginException.NOT_TOKEN:
                return "未提供Token";
            case NotLoginException.INVALID_TOKEN:
                return "Token无效";
            case NotLoginException.TOKEN_TIMEOUT:
                return "Token已过期";
            case NotLoginException.BE_REPLACED:
                return "Token已被顶下线";
            case NotLoginException.KICK_OUT:
                return "Token已被踢下线";
            default:
                return "当前会话未登录";
        }
    }
}