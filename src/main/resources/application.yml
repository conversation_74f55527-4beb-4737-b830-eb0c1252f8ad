
spring:
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null
  servlet:
    multipart:
      enabled: true
      max-file-size: 500MB
      max-request-size: 2000MB
  datasource:
    #    url: ****************************************************************************************************************************
    url: *********************************************************************************************************************************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: root
    # Druid 配置
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 10
      min-idle: 5
      max-active: 50
      max-wait: 10000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true # 是否在空闲连接回收时，检查连接是否有效
      test-on-borrow: false # 指定从连接池中获取连接时，是否要执行验证查询来检查连接是否有效
      test-on-return: false # 归还连接池时，检查连接是否有效
      filters: stat
      connection-properties: druid.stat.mergeSql=true;druid.stat.slowSqlMillis=5000
mybatis-plus:
  mapper-locations: classpath*:/com.guanghonggu.mapper/*.xml  # Mapper XML 文件路径
  typeAliasesPackage: com.guanghonggu                         # 指定实体类的包路径
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl     #开启sql打印
server:
  port: 8082

alipay:
  app-id: 2021000148645593  # 沙箱应用AppId
  private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCTRMgMcRemvAQ138speU+coTnga2fF2MYRHbagwt78JURRR4RHNpR4cbxrPxHDGX2HFTEv1cE6cclsC6HuGTjLaP7OryF9EOVnP6Zn9q+zfhamHFocf6PQp/vHqt6vxaj/yjdsLmUH0wOqebGLbc0MZuXz3VfQXbXHgXv/4yAgkEkoCe5V0HmmUWloxnzwJnbwOePlkevU1SOP8Co/dz3gKGiygSzGc+OXaTtNcS5u6d3oBWz0U/whHJLFUHaj3Oi/gyVFTM9pCxQsHjPGlJxzu1SaoeuhGIhbFly4Py5a10DMvYyXvu5/Dn2c5mRVWNEkmIuXYGa/HgVetLC4WHZzAgMBAAECggEAM7cFoQjdy1lfCSQrjcbh5Bjh4CMXb+ytsTkD9vQ3pcfYc8pvcyqBfMJD7k1ZiW+cGyGgAjJIBqQKnXyAQsJspVGbmSz6z5ZtWJKLdxqznI58mZx/MF7KQe2bhwRD6um4bQqv57drFxJMyUKuzE2koeRIgBiMS8l8mjCoQLWbhJNZ1udleNDAAq8ztk0sIzth/Tv7Sb/L5Wk0qRDqPISOjkS6yVWIATuGAEJKu010PFczfPEtN/PlRzwvHDzNKMf1bLgEJEeCF/jHS3n7HJyW4I4kJj1q5oWADJJA+aeHCk80pKP6iZK0Rmqynu7x+DLdRWblpkaosxGaeBUo2ezK8QKBgQD5Snq4ZkCm4owqYq9kWvNpqE70v5hadn9n53PiEKmgsWIozINKOrxCSgIQHsSoAG4M3Q5aGeveBGPtmAkn5Dm/rQOsu9n0aY3k4ZvMnlPY2ivXvxe0uF476lHeCNokz0v8RsgagQgNXgZvq+GaFq2X22o5QFY4FHpKTkVOcKTr3wKBgQCXO2g6rA+aTqtYjAgS/oYLBu7/51B4SEaJjdwyrQxF1J0rV1adWRwhOhbM4WJ4CjLvrQHcn7SzlFQx14+BxC+EO/khxOAKi9wk/NoeHNfrCBicxzufzlDRUq+QryqnDthSYpQfbDJxWJJ88AAKkkPYqaptg2ARgNsQoYOYqakH7QKBgQC/fPN5U6oTxu+N21IIj0hsf4luvQFlc6J4CLTeEJiLw9W3qyiqSyWy8bBV+5xpxDLElMTidwjT+KFdryxRRSEK1/Z7p6xK7w28o4QOkAuuBiApUDMAcaKI+Xu7RydxoP4oIaKxoS2zPudIvknEF+luPW+B3Kad2zT4gNkB4Mr+FwKBgFzleBv6T0dJJcWpltgcEri5VJxLOcn5E6MxSd6zH8PRW44hAyp6b2YVqaXGmNcF5M4HJwqf678X0TQgpp1eppWCxYExzzGLQfpSe6VdsGBGQ+AiHgUHFevO79tfTkL6QnUw6q32p3Gqp6kpP80+lYCEkBTfS2bVETvBCvX+2b7tAoGAOv9RBRzar1FhxdcJ8PLPQrM3AjJtWCWxThD/OUekP8GSY+oYQlZytc3zP1Kqbowdc+ahhm1XRptYhAs3B112TfiomE8ykXzYVBl19kYHSMVjQVkwfc6Vg7xOqNOcgeKn6SgCpyOaKnKotoyUd1DjYWsD/VIlxe1czq8o1wY+IQ8=
  alipay-public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAp/AKEov/hF3yWSAuyOgfYCK8udILqTI7Jk9WITQePZJR1ajmjL892I76sz6NrEeCrH2c1ayuyXPsS/PLL7jptQL4bpScdhl1jwd5CBJwc05o19L6uDvemjsF1nRYe/HtASuECfRhPoTn9NfRqWdFli/oUBBoRlaq40+IIH00Ol3JWCK/Rio1fM9c1wtJmnIzxoi9ahJ9HGOfeZx/syvgN3zF1HRkaB5A+QOiXR/dCP+pdKZ2vxCJGTJ1xILOiwQnny2ZjGro6JR6bx54q3lBhbRA3uop/cf/d6jKHE1fpZA2D7KLajk+1MVtYiQJ5b5jBT0THugjoeHzYN3VJSpQ7QIDAQAB
  notify-url: http://w86c73a3.natappfree.cc/wallet/rechargeNotify
  charset: UTF-8
  sign-type: RSA2
  return-url: http://localhost:8080/pay/success
  gateway-url: https://openapi-sandbox.dl.alipaydev.com/gateway.do

aliyun:
  oss:
    endpoint: oss-cn-beijing.aliyuncs.com
    accessKeyId: LTAI5tHZtvMzbLrtG59kA4Xu
    accessKeySecret: ******************************
    bucketName: diligent-sister
    fileHost: complaint
  accessKey:
    id: ${ALIYUN_ACCESS_KEY_ID:LTAI5tHZtvMzbLrtG59kA4Xu}  # 从环境变量ALIYUN_ACCESS_KEY_ID获取，如果没有则使用your-access-key-id
    secret: ${ALIYUN_ACCESS_KEY_SECRET:******************************}  # 从环境变量ALIYUN_ACCESS_KEY_SECRET获取，如果没有则使用your-access-key-secret
    regionId: cn-beijing
    chatbot:
      robot-id: chatbot-cn-VBm7eay72b
      region: cn-shanghai
      endpoint: chatbot.cn-shanghai.aliyuncs.com
    agentKey: edc5e6a15b06484d8456ddf641670d24_p_beebot_public




wx:
  appId: your_app_id
  appSecret: your_app_secret
  redirectUri: http://yourdomain.com/login/wx-callback   # 微信登录回调地址
  mch-id: your_mch_id
  api-key: your_api_key
  notify-url: http://q283a4dd.natappfree.cc/apppay/appnotify          # 支付回调地址



############## Sa-Token 配置 (文档: https://sa-token.cc) ##############
sa-token:
  # token 名称（同时也是 cookie 名称）
  token-name: token
  # token 有效期（单位：秒） 默认30天，-1 代表永久有效
  timeout: 2592000
  # token 最低活跃频率（单位：秒），如果 token 超过此时间没有访问系统就会被冻结，默认-1 代表不限制，永不冻结
  active-timeout: -1
  # 是否允许同一账号多地同时登录 （为 true 时允许一起登录, 为 false 时新登录挤掉旧登录）
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个 token （为 true 时所有登录共用一个 token, 为 false 时每次登录新建一个 token）
  is-share: false
  # token 风格（默认可取值：uuid、simple-uuid、random-32、random-64、random-128、tik）
  token-style: uuid
  # 是否输出操作日志
  is-log: true

amap:
  key: 8c3fd077eeb29017a98727ebc0d31c0d



