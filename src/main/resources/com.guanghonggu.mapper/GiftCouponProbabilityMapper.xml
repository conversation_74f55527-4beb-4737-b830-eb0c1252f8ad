<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.GiftCouponProbabilityMapper">
    <select id="getGiftCouponProbabilityWithCoupon" resultType="com.guanghonggu.entity.GiftCouponProbability">
        SELECT
            gcp.*,
            c.title AS coupon_title,
            c.description AS coupon_description
        FROM
            gift_coupon_probability gcp
                LEFT JOIN
            coupon c ON gcp.coupon_id = c.coupon_id
    </select>

</mapper>
