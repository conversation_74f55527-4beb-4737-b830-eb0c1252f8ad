<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.CouponMapper">
    <select id="getCouponById" resultType="com.guanghonggu.entity.Coupon">
        SELECT * FROM coupon WHERE coupon_id = #{couponId}
    </select>

    <update id="updateCouponRemainingQuantity">
        UPDATE coupon
        SET remaining_quantity = remaining_quantity - 1
        WHERE coupon_id = #{couponId} AND remaining_quantity > 0
    </update>
</mapper>
