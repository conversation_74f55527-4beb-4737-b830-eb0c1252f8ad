<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.ComplaintMapper">

    <select id="getAllComplaintWithCaregiverName" resultMap="complaintMap">
        SELECT
            cl.*,
            c.name AS caregiver_name,
            u.nickname AS user_name
        FROM
            `complaint` cl
                LEFT JOIN
            caregiver c ON cl.caregiver_id = c.caregiver_id
                LEFT JOIN
            user u ON cl.user_id = u.user_id
    </select>

    <!-- 映射查询结果 -->
    <resultMap id="complaintMap" type="com.guanghonggu.entity.Complaint">
        <result column="caregiver_name" property="caregiverName"/>
        <result column="user_name" property="userName" />
    </resultMap>

</mapper>
