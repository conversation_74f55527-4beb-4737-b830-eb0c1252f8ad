<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.UserVipMapper">
    <select id="getUserVipWithCoupon" resultType="com.guanghonggu.entity.UserVip">
        SELECT
            uv.*,
            c.title AS coupon_title,
            c.description AS coupon_description
        FROM
            user_vip uv
                LEFT JOIN
            coupon c ON uv.coupon_id = c.coupon_id
    </select>

</mapper>
