<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.OrderMapper">

    <!-- resultMap for Order Entity -->
    <resultMap id="OrderResultMap" type="com.guanghonggu.entity.Order">
        <id property="orderId" column="order_id"/>
        <result property="orderNumber" column="order_number"/>
        <result property="userId" column="user_id"/>
        <result property="caregiversId" column="caregivers_id"/>
        <result property="addressId" column="address_id"/>
        <result property="status" column="status"/>
        <result property="serviceTypeId" column="service_type_id"/>
        <result property="appointmentTime" column="appointment_time"/>
        <result property="orderCompletionTime" column="order_completion_time"/>
        <result property="serviceEndTime" column="service_end_time"/>
        <result property="totalPrice" column="total_price"/>
        <result property="preferentialPrice" column="preferential_price"/>
        <result property="actualPaymentPrice" column="actual_payment_price"/>
        <result property="payMethods" column="pay_methods"/>
        <result property="payTime" column="pay_time"/>
        <result property="payOrderNumber" column="pay_order_number"/>
        <result property="couponId" column="coupon_id"/>
        <result property="orderType" column="order_type"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="caregiverIncome" column="caregiver_income"/>
        <result property="merchantIncome" column="merchant_income"/>
        <!-- 非数据库字段 -->
        <result property="serviceTypeName" column="serviceTypeName"/>
    </resultMap>

    <!-- 联表查询 Order 表和 Caregiver 表，并返回 caregiver_name 字段 -->
    <select id="getAllOrderWithCaregiverName" resultMap="orderResultMap">
        SELECT
            o.*,
            c.name AS caregiver_name,
            u.nickname AS user_name
        FROM
            `order` o
                LEFT JOIN
            caregiver c ON o.caregivers_id = c.caregiver_id
                LEFT JOIN
            user u ON o.user_id = u.user_id
    </select>
    <select id="getOrderWithCaregiverAndUser" resultType="com.guanghonggu.entity.Order">
        SELECT
            o.*,
            o.order_number AS orderNumber,
            c.name AS caregiver_name,
            u.nickname AS user_name
        FROM
            `order` o
                LEFT JOIN
            caregiver c ON o.caregivers_id = c.caregiver_id
                LEFT JOIN
            user u ON o.user_id = u.user_id
        <where>
            <if test="orderNumber != null and orderNumber != ''">
                AND o.order_number = #{orderNumber}
            </if>
        </where>
    </select>

    <!-- 映射查询结果 -->
    <resultMap id="orderResultMap" type="com.guanghonggu.entity.Order">
        <result column="caregiver_name" property="caregiverName"/>
        <result column="user_name" property="userName" />
    </resultMap>

    <!-- 查询城市下指定服务类型订单数量 -->
    <select id="countOrderTypeByRegionWithCondition"
            parameterType="map"
            resultType="com.guanghonggu.dto.StatisticsDTO">
        SELECT
        a.area AS area,
        o.service_type_id AS serviceTypeId,
        COUNT(*) AS count
        FROM `order` o
        LEFT JOIN address a ON o.address_id = a.address_id
        <where>
            <if test="area != null and area != ''">
                AND a.area = #{area}
            </if>
            <if test="serviceTypeId != null">
                AND o.service_type_id = #{serviceTypeId}
            </if>
        </where>
        GROUP BY a.area, o.service_type_id
    </select>
    <select id="getOrderById" resultType="com.guanghonggu.dto.OrderDTO">
        SELECT
            o.order_id,
            o.order_number,
            o.user_id,
            o.caregivers_id,
            o.address_id,
            o.status,
            o.service_type_id,
            o.appointment_time,
            o.order_completion_time,
            o.service_end_time,
            o.total_price,
            o.preferential_price,
            o.actual_payment_price,
            o.pay_methods,
            o.pay_time,
            o.pay_order_number,
            o.order_type,
            o.estimated_time,
            o.create_time,
            o.update_time,
            csv.service_verification_id
        FROM
            `order` o
                LEFT JOIN
            caregiver_service_verification csv ON o.order_id = csv.order_id
        WHERE
            o.order_id = #{orderId}

    </select>
    <select id="getServiceVerificationImagesByOrderId" resultType="com.guanghonggu.dto.ServiceVerificationImageDTO">
        SELECT
        s.image_url AS imageUrl
        FROM
        service_verification_image s
        WHERE
        s.service_verification_id = #{serviceVerificationId}  <!-- 使用 service_verification_id -->
    </select>

    <!-- OrderMapper.xml 中需要添加的 SQL 映射 -->

    <!-- 汇总所有订单的商户收入 -->
    <select id="sumMerchantIncome" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(merchant_income), 0)
        FROM `order`
    </select>

    <!-- 按年汇总商户收入 -->
    <select id="sumMerchantIncomeByYear" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(merchant_income), 0)
        FROM `order`
        WHERE YEAR(create_time) = #{year}
    </select>

    <!-- 按月汇总商户收入 -->
    <select id="sumMerchantIncomeByMonth" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(merchant_income), 0)
        FROM `order`
        WHERE YEAR(create_time) = #{year}
          AND MONTH(create_time) = #{month}
    </select>

    <!-- 按周汇总商户收入 -->
    <select id="sumMerchantIncomeByWeek" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(merchant_income), 0)
        FROM `order`
        WHERE YEAR(create_time) = #{year}
        AND WEEK(create_time, 1) = #{week}
    </select>

    <!-- 按日汇总商户收入 -->
    <select id="sumMerchantIncomeByDay" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(merchant_income), 0)
        FROM `order`
        WHERE DATE(create_time) = #{date}
    </select>
</mapper>
