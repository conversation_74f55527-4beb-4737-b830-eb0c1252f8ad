<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.guanghonggu.mapper.UserMessageMapper">

    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO user_message (user_id, user_type, message_id, is_read, read_time, create_time)
        VALUES
        <foreach collection="userMessages" item="item" separator=",">
            (#{item.userId}, #{item.userType}, #{item.messageId}, #{item.isRead}, #{item.readTime}, #{item.createTime})
        </foreach>
    </insert>

</mapper>
